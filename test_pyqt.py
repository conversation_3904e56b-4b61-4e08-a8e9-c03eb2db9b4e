#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple PyQt5 test application
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget

class TestWindow(QMainWindow):
    """Test window to verify PyQt5 is working"""
    
    def __init__(self):
        """Initialize the window"""
        super().__init__()
        
        # Set window properties
        self.setWindowTitle("PyQt5 Test")
        self.setGeometry(100, 100, 400, 200)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create label
        label = QLabel("PyQt5 is working!")
        label.setStyleSheet("font-size: 18pt;")
        layout.addWidget(label)
        
        # Create button
        button = QPushButton("Click Me")
        button.clicked.connect(self.on_button_clicked)
        layout.addWidget(button)
    
    def on_button_clicked(self):
        """Handle button click"""
        print("Button clicked!")

def main():
    """Main function"""
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
