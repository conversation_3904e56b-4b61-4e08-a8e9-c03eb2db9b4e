#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pie Chart Widget for Email Checker Pro
"""

from PyQt5.QtWidgets import QWidget
from PyQt5.QtGui import QPainter, QColor, QBrush, QPen, QFont
from PyQt5.QtCore import Qt, QRect, QPoint

class PieChartWidget(QWidget):
    """Widget for displaying a 3D pie chart"""

    def __init__(self, parent=None):
        """Initialize the widget

        Args:
            parent: Parent widget
        """
        super().__init__(parent)

        # Set default values
        self.data = {
            'Wait': 0,
            'OK': 0,
            'BAD': 0,
            'Server Not Found': 0,
            'Checking Failed': 0
        }

        # Set professional colors with gradients
        self.colors = {
            'Wait': QColor(33, 150, 243),      # Material Blue
            'OK': QColor(76, 175, 80),         # Material Green
            'BAD': QColor(244, 67, 54),        # Material Red
            'Server Not Found': QColor(255, 152, 0),  # Material Orange
            'Checking Failed': QColor(158, 158, 158)  # Material Grey
        }

        # Set shadow colors for 3D effect
        self.shadow_colors = {
            'Wait': QColor(21, 101, 192),      # Darker Blue
            'OK': QColor(56, 142, 60),         # Darker Green
            'BAD': QColor(198, 40, 40),        # Darker Red
            'Server Not Found': QColor(230, 126, 34),  # Darker Orange
            'Checking Failed': QColor(117, 117, 117)   # Darker Grey
        }

        # Set minimum size for better visibility
        self.setMinimumSize(280, 320)

    def set_data(self, data):
        """Set the data for the pie chart

        Args:
            data (dict): Dictionary with status as key and count as value
        """
        self.data = data
        self.update()

    def paintEvent(self, event):
        """Paint the widget

        Args:
            event: Paint event
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Calculate total
        total = sum(self.data.values())

        if total == 0:
            # Draw empty pie chart
            self._draw_empty_pie_chart(painter)
        else:
            # Draw pie chart
            self._draw_pie_chart(painter, total)

    def _draw_empty_pie_chart(self, painter):
        """Draw an empty pie chart

        Args:
            painter: QPainter object
        """
        # Set pen and brush
        painter.setPen(QPen(Qt.black, 1))
        painter.setBrush(QBrush(Qt.lightGray))

        # Calculate size
        width = self.width()
        height = self.height()
        size = min(width, height) - 20

        # Draw circle
        painter.drawEllipse(
            (width - size) // 2,
            (height - size) // 2,
            size,
            size
        )

        # Draw text
        painter.setPen(QPen(Qt.black))
        painter.setFont(QFont('Arial', 10))
        painter.drawText(
            QRect(0, 0, width, height),
            Qt.AlignCenter,
            "No Data"
        )

    def _draw_pie_chart(self, painter, total):
        """Draw a professional 3D pie chart with data

        Args:
            painter: QPainter object
            total: Total count
        """
        # Calculate size with better proportions
        width = self.width()
        height = self.height()
        size = min(width, height) - 40

        # Calculate center and radius
        center_x = width // 2
        center_y = height // 2 - 10
        radius = size // 2

        # 3D effect parameters
        shadow_offset = 8
        shadow_depth = 6

        # Draw multiple shadow layers for better 3D effect
        for i in range(shadow_depth):
            shadow_alpha = 50 - (i * 8)
            shadow_color = QColor(0, 0, 0, shadow_alpha)
            painter.setPen(QPen(shadow_color, 1))
            painter.setBrush(QBrush(shadow_color))
            painter.drawEllipse(
                center_x - radius + i,
                center_y - radius + shadow_offset + i,
                size - (i * 2),
                size - (i * 2)
            )

        # Draw pie slices with professional styling
        start_angle = 0
        for status, count in self.data.items():
            if count > 0:
                # Calculate angle
                angle = int(360 * count / total)

                # Get colors
                main_color = self.colors.get(status, QColor(128, 128, 128))
                shadow_color = self.shadow_colors.get(status, QColor(64, 64, 64))

                # Draw shadow slice first
                painter.setPen(QPen(shadow_color, 2))
                painter.setBrush(QBrush(shadow_color))
                painter.drawPie(
                    center_x - radius + 2,
                    center_y - radius + shadow_offset,
                    size - 4,
                    size - 4,
                    start_angle * 16,
                    angle * 16
                )

                # Draw main slice with gradient effect
                painter.setPen(QPen(main_color.darker(120), 2))
                painter.setBrush(QBrush(main_color))
                painter.drawPie(
                    center_x - radius,
                    center_y - radius,
                    size,
                    size,
                    start_angle * 16,
                    angle * 16
                )

                # Add highlight effect on top edge
                highlight_color = main_color.lighter(150)
                painter.setPen(QPen(highlight_color, 1))
                painter.setBrush(QBrush(highlight_color))
                painter.drawPie(
                    center_x - radius + 2,
                    center_y - radius - 2,
                    size - 4,
                    size - 4,
                    start_angle * 16,
                    angle * 16
                )

                # Update start angle
                start_angle += angle

        # Draw center highlight circle for more professional look
        center_highlight_radius = radius // 4
        painter.setPen(QPen(QColor(255, 255, 255, 100), 2))
        painter.setBrush(QBrush(QColor(255, 255, 255, 50)))
        painter.drawEllipse(
            center_x - center_highlight_radius,
            center_y - center_highlight_radius - 5,
            center_highlight_radius * 2,
            center_highlight_radius * 2
        )

        # Draw professional legend
        self._draw_legend(painter, width, height)

    def _draw_legend(self, painter, width, height):
        """Draw a professional legend

        Args:
            painter: QPainter object
            width: Widget width
            height: Widget height (unused but kept for compatibility)
        """
        # Set professional font
        painter.setFont(QFont('Segoe UI', 9, QFont.Weight.Medium))

        # Calculate legend position (bottom of the chart)
        legend_start_y = height - 80
        legend_x = 10

        # Draw legend background
        legend_bg_rect = QRect(5, legend_start_y - 10, width - 10, 75)
        painter.setPen(QPen(QColor(222, 226, 230), 2))
        painter.setBrush(QBrush(QColor(248, 249, 250, 200)))
        painter.drawRoundedRect(legend_bg_rect, 8, 8)

        # Draw legend title
        painter.setPen(QPen(QColor(73, 80, 87)))
        painter.setFont(QFont('Segoe UI', 10, QFont.Weight.Bold))
        painter.drawText(QPoint(legend_x + 5, legend_start_y + 5), "📋 Legend")

        # Reset font for items
        painter.setFont(QFont('Segoe UI', 8, QFont.Weight.Medium))

        # Calculate items per row (2 columns)
        items_with_data = [(status, count) for status, count in self.data.items() if count > 0]
        items_per_row = 2
        legend_y = legend_start_y + 20

        # Draw legend items in a grid
        for i, (status, count) in enumerate(items_with_data):
            col = i % items_per_row
            row = i // items_per_row

            item_x = legend_x + 5 + (col * (width // 2 - 20))
            item_y = legend_y + (row * 18)

            # Get color
            color = self.colors.get(status, QColor(128, 128, 128))

            # Draw professional color indicator (rounded rectangle)
            painter.setPen(QPen(color.darker(120), 1))
            painter.setBrush(QBrush(color))
            painter.drawRoundedRect(item_x, item_y - 8, 12, 12, 3, 3)

            # Add small highlight
            painter.setPen(QPen(color.lighter(150), 1))
            painter.setBrush(QBrush(color.lighter(130)))
            painter.drawRoundedRect(item_x + 1, item_y - 7, 10, 6, 2, 2)

            # Draw text with professional styling
            painter.setPen(QPen(QColor(73, 80, 87)))
            text = f"{status}: {count}"
            if len(text) > 20:  # Truncate long text
                text = text[:17] + "..."
            painter.drawText(QPoint(item_x + 18, item_y + 2), text)
