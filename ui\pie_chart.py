#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pie Chart Widget for Email Checker Pro
"""

from PyQt5.QtWidgets import QWidget
from PyQt5.QtGui import QPainter, QColor, QBrush, QPen, QFont
from PyQt5.QtCore import Qt, QRect, QPoint

class PieChartWidget(QWidget):
    """Widget for displaying a 3D pie chart"""
    
    def __init__(self, parent=None):
        """Initialize the widget
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Set default values
        self.data = {
            'Wait': 0,
            'OK': 0,
            'BAD': 0,
            'Server Not Found': 0,
            'Checking Failed': 0
        }
        
        # Set colors
        self.colors = {
            'Wait': QColor(0, 0, 255),  # Blue
            'OK': QColor(0, 128, 0),    # Green
            'BAD': QColor(255, 0, 0),   # Red
            'Server Not Found': QColor(255, 165, 0),  # Orange
            'Checking Failed': QColor(128, 128, 128)  # Gray
        }
        
        # Set minimum size
        self.setMinimumSize(200, 200)
    
    def set_data(self, data):
        """Set the data for the pie chart
        
        Args:
            data (dict): Dictionary with status as key and count as value
        """
        self.data = data
        self.update()
    
    def paintEvent(self, event):
        """Paint the widget
        
        Args:
            event: Paint event
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Calculate total
        total = sum(self.data.values())
        
        if total == 0:
            # Draw empty pie chart
            self._draw_empty_pie_chart(painter)
        else:
            # Draw pie chart
            self._draw_pie_chart(painter, total)
    
    def _draw_empty_pie_chart(self, painter):
        """Draw an empty pie chart
        
        Args:
            painter: QPainter object
        """
        # Set pen and brush
        painter.setPen(QPen(Qt.black, 1))
        painter.setBrush(QBrush(Qt.lightGray))
        
        # Calculate size
        width = self.width()
        height = self.height()
        size = min(width, height) - 20
        
        # Draw circle
        painter.drawEllipse(
            (width - size) // 2,
            (height - size) // 2,
            size,
            size
        )
        
        # Draw text
        painter.setPen(QPen(Qt.black))
        painter.setFont(QFont('Arial', 10))
        painter.drawText(
            QRect(0, 0, width, height),
            Qt.AlignCenter,
            "No Data"
        )
    
    def _draw_pie_chart(self, painter, total):
        """Draw a pie chart with data
        
        Args:
            painter: QPainter object
            total: Total count
        """
        # Calculate size
        width = self.width()
        height = self.height()
        size = min(width, height) - 20
        
        # Calculate center and radius
        center_x = width // 2
        center_y = height // 2
        radius = size // 2
        
        # Draw 3D effect (bottom circle)
        painter.setPen(QPen(Qt.black, 1))
        painter.setBrush(QBrush(Qt.darkGray))
        painter.drawEllipse(
            center_x - radius,
            center_y - radius + 10,
            size,
            size
        )
        
        # Draw pie slices
        start_angle = 0
        for status, count in self.data.items():
            if count > 0:
                # Calculate angle
                angle = int(360 * count / total)
                
                # Set color
                color = self.colors.get(status, Qt.gray)
                painter.setBrush(QBrush(color))
                
                # Draw slice
                painter.drawPie(
                    center_x - radius,
                    center_y - radius,
                    size,
                    size,
                    start_angle * 16,
                    angle * 16
                )
                
                # Update start angle
                start_angle += angle
        
        # Draw legend
        self._draw_legend(painter, width, height)
    
    def _draw_legend(self, painter, width, height):
        """Draw the legend
        
        Args:
            painter: QPainter object
            width: Widget width
            height: Widget height
        """
        # Set font
        painter.setFont(QFont('Arial', 8))
        
        # Calculate legend position
        legend_x = width - 80
        legend_y = 20
        
        # Draw legend items
        for status, count in self.data.items():
            if count > 0:
                # Set color
                color = self.colors.get(status, Qt.gray)
                painter.setBrush(QBrush(color))
                painter.setPen(QPen(Qt.black, 1))
                
                # Draw color box
                painter.drawRect(legend_x, legend_y, 10, 10)
                
                # Draw text
                painter.setPen(QPen(Qt.black))
                painter.drawText(
                    QPoint(legend_x + 15, legend_y + 10),
                    f"{status}: {count}"
                )
                
                # Update position
                legend_y += 20
