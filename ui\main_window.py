#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main Window for Email Checker Pro
"""

import os
import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QHeaderView, QToolBar, QAction, QStatusBar,
    QProgressBar, QLabel, QFileDialog, QMessageBox, QMenu, QSplitter,
    QFrame, QPushButton, QDialog, QGroupBox, QRadioButton, QSpinBox,
    QApplication
)
from PyQt5.QtGui import QIcon, QColor, QPixmap
from PyQt5.QtCore import Qt, QSize, pyqtSlot, QTimer, QPoint

from core.db_manager import DatabaseManager
from core.email_validator import EmailValidator
from core.import_export import ImportExportManager
from ui.options_dialog import OptionsDialog
from ui.import_dialog import ImportDialog
from config import Config

class MainWindow(QMainWindow):
    """Main window of the application"""

    def __init__(self):
        """Initialize the main window"""
        super().__init__()

        # Initialize components
        self.config = Config()
        self.db_manager = DatabaseManager()
        self.email_validator = EmailValidator(self.db_manager)
        self.import_export_manager = ImportExportManager(self.db_manager)

        # Set up the UI
        self.setup_ui()

        # Load emails from database
        self.load_emails()

    def setup_ui(self):
        """Set up the user interface"""
        # Set window properties
        self.setWindowTitle("Email Checker Pro")
        self.resize(
            self.config.get('window_width', 800),
            self.config.get('window_height', 600)
        )

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QHBoxLayout(central_widget)

        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Create left panel (table)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # Create table
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "Email Address", "Name", "Result", "Description", "Suggestion", "Last Check"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.verticalHeader().setVisible(False)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.setAlternatingRowColors(True)
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        # Set table style
        self.table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                background-color: #ffffff;
                gridline-color: #e0e0e0;
                alternate-background-color: #f8f8f8;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #e0e0ff;
                color: black;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 6px;
                border: 1px solid #d0d0d0;
                border-bottom: 2px solid #c0c0c0;
                font-weight: bold;
            }
        """)

        left_layout.addWidget(self.table)

        # Create right panel (stats and controls)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Create counts group
        counts_group = QGroupBox("Counts")
        counts_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #c0c0c0;
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)
        counts_layout = QVBoxLayout(counts_group)

        self.counts_labels = {
            'All': QLabel("0"),
            'Wait': QLabel("0"),
            'OK': QLabel("0"),
            'BAD': QLabel("0"),
            'Server Not Found': QLabel("0"),
            'Checking Failed': QLabel("0")
        }

        for status, label in self.counts_labels.items():
            row_layout = QHBoxLayout()
            status_label = QLabel(status)

            # Set status icon and style
            if status == 'Wait':
                status_label.setStyleSheet("color: blue; font-weight: bold;")
                label.setStyleSheet("color: blue; font-weight: bold;")
            elif status == 'OK':
                status_label.setStyleSheet("color: green; font-weight: bold;")
                label.setStyleSheet("color: green; font-weight: bold;")
            elif status == 'BAD':
                status_label.setStyleSheet("color: red; font-weight: bold;")
                label.setStyleSheet("color: red; font-weight: bold;")
            elif status == 'Server Not Found':
                status_label.setStyleSheet("color: orange; font-weight: bold;")
                label.setStyleSheet("color: orange; font-weight: bold;")
            elif status == 'Checking Failed':
                status_label.setStyleSheet("color: gray; font-weight: bold;")
                label.setStyleSheet("color: gray; font-weight: bold;")
            else:
                status_label.setStyleSheet("font-weight: bold;")
                label.setStyleSheet("font-weight: bold;")

            row_layout.addWidget(status_label)
            row_layout.addStretch()
            row_layout.addWidget(label)
            counts_layout.addLayout(row_layout)

        right_layout.addWidget(counts_group)

        # Create pie chart group
        pie_chart_group = QGroupBox("3D Pie Chart")
        pie_chart_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #c0c0c0;
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)
        pie_chart_layout = QVBoxLayout(pie_chart_group)

        # Add pie chart widget
        from ui.pie_chart import PieChartWidget
        self.pie_chart = PieChartWidget()
        pie_chart_layout.addWidget(self.pie_chart)

        right_layout.addWidget(pie_chart_group)

        # Create single mode group
        single_mode_group = QGroupBox("Single Mode")
        single_mode_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #c0c0c0;
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)
        single_mode_layout = QVBoxLayout(single_mode_group)

        self.single_mode_button = QPushButton("Enable")
        self.single_mode_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px;
                text-align: center;
                font-weight: bold;
            }
            QPushButton:checked {
                background-color: #e0e0e0;
                border: 1px solid #a0a0a0;
            }
        """)
        self.single_mode_button.setCheckable(True)
        self.single_mode_button.setChecked(self.config.get('single_mode', False))
        self.single_mode_button.toggled.connect(self.toggle_single_mode)
        single_mode_layout.addWidget(self.single_mode_button)

        right_layout.addWidget(single_mode_group)

        # Create tip group
        tip_group = QGroupBox("Tip on Recheck")
        tip_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #c0c0c0;
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)
        tip_layout = QVBoxLayout(tip_group)

        tip_label = QLabel("If you always get the 'Recheck' suggestion for an item, we recommend that you discard this email address.")
        tip_label.setWordWrap(True)
        tip_label.setStyleSheet("font-style: italic;")
        tip_layout.addWidget(tip_label)

        right_layout.addWidget(tip_group)

        # Create interaction group
        interaction_group = QGroupBox("Interaction of Selection")
        interaction_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #c0c0c0;
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)
        interaction_layout = QVBoxLayout(interaction_group)

        interaction_label = QLabel("N/A")
        interaction_label.setAlignment(Qt.AlignCenter)
        interaction_layout.addWidget(interaction_label)

        right_layout.addWidget(interaction_group)

        # Add stretch to push everything to the top
        right_layout.addStretch()

        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)

        # Set initial splitter sizes (70% left, 30% right)
        splitter.setSizes([int(self.width() * 0.7), int(self.width() * 0.3)])

        # Create toolbar
        self.create_toolbar()

        # Create status bar
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)

        # Add progress bar to status bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(200)
        self.progress_bar.setVisible(False)
        self.statusBar.addPermanentWidget(self.progress_bar)

        # Add status label to status bar
        self.status_label = QLabel("Ready")
        self.statusBar.addWidget(self.status_label)

    def create_toolbar(self):
        """Create the toolbar with actions"""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setIconSize(QSize(32, 32))
        self.addToolBar(toolbar)

        # Get the absolute path to the icons directory
        icons_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'resources', 'icons'))

        # Check action
        check_icon_path = os.path.join(icons_dir, 'check.png')
        check_action = QAction(QIcon(check_icon_path), "Check", self)
        check_action.setStatusTip("Check email addresses")
        check_action.triggered.connect(self.check_emails)
        toolbar.addAction(check_action)

        # Import action
        import_icon_path = os.path.join(icons_dir, 'import.png')
        import_action = QAction(QIcon(import_icon_path), "Import", self)
        import_action.setStatusTip("Import email addresses")
        import_action.triggered.connect(self.import_emails)
        toolbar.addAction(import_action)

        # Export action
        export_icon_path = os.path.join(icons_dir, 'export.png')
        export_action = QAction(QIcon(export_icon_path), "Export", self)
        export_action.setStatusTip("Export email addresses")
        export_action.triggered.connect(self.export_emails)
        toolbar.addAction(export_action)

        # Clear action
        clear_icon_path = os.path.join(icons_dir, 'clear.png')
        clear_action = QAction(QIcon(clear_icon_path), "Clear", self)
        clear_action.setStatusTip("Clear all email addresses")
        clear_action.triggered.connect(self.clear_emails)
        toolbar.addAction(clear_action)

        # Delete action
        delete_icon_path = os.path.join(icons_dir, 'delete.png')
        delete_action = QAction(QIcon(delete_icon_path), "Delete", self)
        delete_action.setStatusTip("Delete selected email addresses")
        delete_action.triggered.connect(self.delete_selected_emails)
        toolbar.addAction(delete_action)

        # Find action
        find_icon_path = os.path.join(icons_dir, 'find.png')
        find_action = QAction(QIcon(find_icon_path), "Find", self)
        find_action.setStatusTip("Find email addresses")
        find_action.triggered.connect(self.find_emails)
        toolbar.addAction(find_action)

        # Options action
        options_icon_path = os.path.join(icons_dir, 'options.png')
        options_action = QAction(QIcon(options_icon_path), "Options", self)
        options_action.setStatusTip("Configure options")
        options_action.triggered.connect(self.show_options)
        toolbar.addAction(options_action)

        # Add text under icons
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)

    def load_emails(self):
        """Load emails from the database"""
        emails = self.db_manager.get_all_emails()

        # Clear table
        self.table.setRowCount(0)

        # Add emails to table
        for email_data in emails:
            self.add_email_to_table(email_data)

        # Update counts
        self.update_counts()

    def add_email_to_table(self, email_data):
        """Add an email to the table

        Args:
            email_data (dict): Email data dictionary
        """
        try:
            row = self.table.rowCount()
            self.table.insertRow(row)

            # Set email address with icon
            email = email_data.get('email', '')
            result = email_data.get('result', 'Wait')

            email_item = QTableWidgetItem(email)

            try:
                # Set icon based on result
                icons_dir = os.path.join(os.path.dirname(__file__), '..', 'resources', 'icons')
                if result == 'OK':
                    icon_path = os.path.join(icons_dir, 'ok.png')
                    if os.path.exists(icon_path):
                        email_item.setIcon(QIcon(icon_path))
                        # Make the icon more visible
                        email_item.setData(Qt.DecorationRole, QIcon(icon_path))
                elif result == 'BAD':
                    icon_path = os.path.join(icons_dir, 'bad.png')
                    if os.path.exists(icon_path):
                        email_item.setIcon(QIcon(icon_path))
                        # Make the icon more visible
                        email_item.setData(Qt.DecorationRole, QIcon(icon_path))
                elif result == 'Server Not Found' or result == 'Checking Failed':
                    icon_path = os.path.join(icons_dir, 'warning.png')
                    if os.path.exists(icon_path):
                        email_item.setIcon(QIcon(icon_path))
                        # Make the icon more visible
                        email_item.setData(Qt.DecorationRole, QIcon(icon_path))
            except Exception as e:
                print(f"Error setting icon: {str(e)}")

            self.table.setItem(row, 0, email_item)

            # Set name
            try:
                name_item = QTableWidgetItem(email_data.get('name', ''))
                self.table.setItem(row, 1, name_item)
            except Exception as e:
                print(f"Error setting name: {str(e)}")
                self.table.setItem(row, 1, QTableWidgetItem(''))

            # Set result
            try:
                result_item = QTableWidgetItem(result)

                # Set result color and font
                font = result_item.font()
                font.setBold(True)
                result_item.setFont(font)

                if result == 'Wait':
                    result_item.setForeground(QColor('blue'))
                elif result == 'OK':
                    result_item.setForeground(QColor('#008800'))  # Darker green
                elif result == 'BAD':
                    result_item.setForeground(QColor('#cc0000'))  # Darker red
                elif result == 'Server Not Found':
                    result_item.setForeground(QColor('#ff8c00'))  # Dark orange
                elif result == 'Checking Failed':
                    result_item.setForeground(QColor('#808080'))  # Gray

                self.table.setItem(row, 2, result_item)
            except Exception as e:
                print(f"Error setting result: {str(e)}")
                self.table.setItem(row, 2, QTableWidgetItem('Wait'))

            # Set description
            try:
                description = email_data.get('description', 'Wait for Checking')
                description_item = QTableWidgetItem(description)

                # Set description color based on result
                if result == 'OK':
                    description_item.setForeground(QColor('#008800'))  # Darker green
                elif result == 'BAD':
                    description_item.setForeground(QColor('#cc0000'))  # Darker red
                    # Make text italic for BAD results
                    font = description_item.font()
                    font.setItalic(True)
                    description_item.setFont(font)
                elif result == 'Server Not Found':
                    description_item.setForeground(QColor('#ff8c00'))  # Dark orange
                elif result == 'Checking Failed':
                    description_item.setForeground(QColor('#808080'))  # Gray

                self.table.setItem(row, 3, description_item)
            except Exception as e:
                print(f"Error setting description: {str(e)}")
                self.table.setItem(row, 3, QTableWidgetItem('Wait for Checking'))

            # Set suggestion
            try:
                suggestion = email_data.get('suggestion', 'N/A')
                suggestion_item = QTableWidgetItem(suggestion)

                # Set suggestion color and font
                font = suggestion_item.font()
                font.setBold(True)
                suggestion_item.setFont(font)

                if suggestion == 'Retain':
                    suggestion_item.setForeground(QColor('#008800'))  # Darker green
                elif suggestion == 'Discard':
                    suggestion_item.setForeground(QColor('#cc0000'))  # Darker red

                self.table.setItem(row, 4, suggestion_item)
            except Exception as e:
                print(f"Error setting suggestion: {str(e)}")
                self.table.setItem(row, 4, QTableWidgetItem('N/A'))

            # Set last check
            try:
                from datetime import datetime
                last_check = email_data.get('last_check', None)
                if last_check:
                    if isinstance(last_check, str):
                        last_check_text = last_check
                    else:
                        last_check_text = last_check.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    last_check_text = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                last_check_item = QTableWidgetItem(last_check_text)

                # Set font for last check
                font = last_check_item.font()
                font.setPointSize(8)  # Smaller font
                last_check_item.setFont(font)

                self.table.setItem(row, 5, last_check_item)
            except Exception as e:
                print(f"Error setting last check: {str(e)}")
                self.table.setItem(row, 5, QTableWidgetItem('N/A'))

        except Exception as e:
            print(f"Unexpected error in add_email_to_table: {str(e)}")

    def update_counts(self):
        """Update the counts in the right panel"""
        counts = {
            'All': 0,
            'Wait': 0,
            'OK': 0,
            'BAD': 0,
            'Server Not Found': 0,
            'Checking Failed': 0
        }

        # Count emails by result
        for row in range(self.table.rowCount()):
            result_item = self.table.item(row, 2)
            result = result_item.text() if result_item else 'Wait'

            counts['All'] += 1
            if result in counts:
                counts[result] += 1

        # Update labels
        for status, count in counts.items():
            if status in self.counts_labels:
                self.counts_labels[status].setText(str(count))

        # Update pie chart
        pie_data = {
            'Wait': counts['Wait'],
            'OK': counts['OK'],
            'BAD': counts['BAD'],
            'Server Not Found': counts['Server Not Found'],
            'Checking Failed': counts['Checking Failed']
        }
        self.pie_chart.set_data(pie_data)

    @pyqtSlot()
    def check_emails(self):
        """Check email addresses"""
        # Get emails from table
        emails = []
        for row in range(self.table.rowCount()):
            email_item = self.table.item(row, 0)
            name_item = self.table.item(row, 1)
            result_item = self.table.item(row, 2)

            if email_item:
                email = email_item.text()
                name = name_item.text() if name_item else None
                result = result_item.text() if result_item else 'Wait'

                # Check if we should recheck this email
                check_history = self.config.get('check_history', 'recheck_if_not_ok')

                if check_history == 'use_old' and result != 'Wait':
                    # Skip this email, use old result
                    continue
                elif check_history == 'always_recheck':
                    # Always recheck
                    pass
                elif check_history == 'recheck_if_not_ok' and result == 'OK':
                    # Skip if result is OK
                    continue
                elif check_history == 'recheck_if_recheck':
                    # TODO: Implement recheck suggestion logic
                    pass
                elif check_history == 'recheck_if_old':
                    # TODO: Implement old check date logic
                    pass

                emails.append({'email': email, 'name': name})

        if not emails:
            QMessageBox.information(self, "No Emails", "No emails to check.")
            return

        # Show progress bar
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)

        # Show checking message in the status bar
        self.status_label.setText("Now checking current list, please wait...")

        # Create a temporary row for the message if table is empty
        if self.table.rowCount() == 0:
            self.table.insertRow(0)
            for col in range(self.table.columnCount()):
                self.table.setItem(0, col, QTableWidgetItem(""))

            message_item = QTableWidgetItem("Now checking current list, please wait...")
            message_item.setTextAlignment(Qt.AlignCenter)
            message_item.setBackground(QColor(51, 102, 153))  # Navy blue background
            message_item.setForeground(QColor(255, 255, 255))  # White text
            font = message_item.font()
            font.setBold(True)
            message_item.setFont(font)
            self.table.setItem(0, 0, message_item)

        # Process events to update UI
        QApplication.processEvents()

        # Start validation
        self.email_validator.start_validation(
            emails,
            on_result=self.on_validation_result,
            on_progress=self.on_validation_progress,
            on_finished=self.on_validation_finished
        )

        self.status_label.setText(f"Checking {len(emails)} email(s)...")

    @pyqtSlot(str, str, str, str)
    def on_validation_result(self, email, result, description, suggestion):
        """Handle validation result

        Args:
            email (str): Email address
            result (str): Validation result
            description (str): Detailed description
            suggestion (str): Suggestion for the user
        """
        # Update database
        self.db_manager.update_email_result(email, result, description, suggestion)

        # Update table
        for row in range(self.table.rowCount()):
            email_item = self.table.item(row, 0)

            if email_item and email_item.text() == email:
                # Update result
                result_item = QTableWidgetItem(result)

                # Set result color
                if result == 'Wait':
                    result_item.setForeground(QColor('blue'))
                elif result == 'OK':
                    result_item.setForeground(QColor('green'))
                elif result == 'BAD':
                    result_item.setForeground(QColor('red'))
                elif result == 'Server Not Found':
                    result_item.setForeground(QColor('orange'))
                elif result == 'Checking Failed':
                    result_item.setForeground(QColor('gray'))

                self.table.setItem(row, 2, result_item)

                # Update description
                description_item = QTableWidgetItem(description)
                self.table.setItem(row, 3, description_item)

                # Update suggestion
                suggestion_item = QTableWidgetItem(suggestion or 'N/A')
                self.table.setItem(row, 4, suggestion_item)

                # Update last check
                from datetime import datetime
                last_check_item = QTableWidgetItem(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                self.table.setItem(row, 5, last_check_item)

                break

        # Update counts
        self.update_counts()

        # Process events to update UI
        QApplication.processEvents()

    @pyqtSlot(int)
    def on_validation_progress(self, progress):
        """Handle validation progress

        Args:
            progress (int): Progress percentage (0-100)
        """
        self.progress_bar.setValue(progress)

    @pyqtSlot()
    def on_validation_finished(self):
        """Handle validation finished"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Email checking completed")

        # Remove any span in the table
        for row in range(self.table.rowCount()):
            for col in range(self.table.columnCount()):
                if self.table.rowSpan(row, col) > 1 or self.table.columnSpan(row, col) > 1:
                    self.table.setSpan(row, col, 1, 1)

        # Update counts
        self.update_counts()

    @pyqtSlot()
    def import_emails(self):
        """Import email addresses from a file"""
        # Show import dialog
        dialog = ImportDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            file_path = dialog.get_file_path()

            if file_path:
                # Show progress bar
                self.progress_bar.setValue(0)
                self.progress_bar.setVisible(True)

                # Start import
                self.import_export_manager.start_import(
                    file_path,
                    on_email_found=self.on_email_imported,
                    on_progress=self.on_import_progress,
                    on_finished=self.on_import_finished,
                    on_error=self.on_import_error
                )

                self.status_label.setText(f"Importing emails from {os.path.basename(file_path)}...")

    @pyqtSlot(str, str)
    def on_email_imported(self, email, name):
        """Handle imported email

        Args:
            email (str): Email address
            name (str): Name associated with the email
        """
        try:
            # Add to database
            try:
                self.db_manager.add_email(email, name)
            except Exception as e:
                print(f"Error adding email to database: {str(e)}")
                # Continue anyway to add to table

            # Add to table
            try:
                self.add_email_to_table({
                    'email': email,
                    'name': name,
                    'result': 'Wait',
                    'description': 'Wait for Checking',
                    'suggestion': 'N/A',
                    'last_check': 'N/A'
                })
            except Exception as e:
                print(f"Error adding email to table: {str(e)}")
        except Exception as e:
            print(f"Unexpected error in on_email_imported: {str(e)}")

        # Update counts
        self.update_counts()

    @pyqtSlot(int)
    def on_import_progress(self, progress):
        """Handle import progress

        Args:
            progress (int): Progress percentage (0-100)
        """
        self.progress_bar.setValue(progress)

    @pyqtSlot(int)
    def on_import_finished(self, count):
        """Handle import finished

        Args:
            count (int): Number of imported emails
        """
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"Imported {count} email(s)")

        # Update counts
        self.update_counts()

        # Ask if user wants to check the imported emails
        if count > 0:
            reply = QMessageBox.question(
                self,
                "Check Emails",
                f"Do you want to check the {count} imported email(s) now?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.check_emails()

    @pyqtSlot(str)
    def on_import_error(self, error):
        """Handle import error

        Args:
            error (str): Error message
        """
        self.progress_bar.setVisible(False)
        self.status_label.setText("Import failed")

        QMessageBox.critical(self, "Import Error", error)

    @pyqtSlot()
    def export_emails(self):
        """Export email addresses to a file"""
        # Get export options
        export_columns = self.config.get('export_columns', ['Name', 'Email Address', 'Result', 'Description', 'Suggestion', 'Last Check'])

        # TODO: Show export dialog

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Emails",
            "",
            "CSV Files (*.csv);;Text Files (*.txt);;Excel Files (*.xlsx)"
        )

        if not file_path:
            return

        # Get emails from table
        emails = []
        for row in range(self.table.rowCount()):
            email_data = {}

            for col, header in enumerate(['Email Address', 'Name', 'Result', 'Description', 'Suggestion', 'Last Check']):
                item = self.table.item(row, col)
                email_data[header] = item.text() if item else ''

            emails.append(email_data)

        if not emails:
            QMessageBox.information(self, "No Emails", "No emails to export.")
            return

        # Show progress bar
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)

        # Start export
        self.import_export_manager.start_export(
            file_path,
            emails,
            export_columns,
            on_progress=self.on_export_progress,
            on_finished=self.on_export_finished
        )

        self.status_label.setText(f"Exporting {len(emails)} email(s) to {os.path.basename(file_path)}...")

    @pyqtSlot(int)
    def on_export_progress(self, progress):
        """Handle export progress

        Args:
            progress (int): Progress percentage (0-100)
        """
        self.progress_bar.setValue(progress)

    @pyqtSlot(bool, str)
    def on_export_finished(self, success, message):
        """Handle export finished

        Args:
            success (bool): True if export was successful, False otherwise
            message (str): Success or error message
        """
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("Export completed")
            QMessageBox.information(self, "Export Completed", message)

            # Remove exported emails if configured
            if self.config.get('remove_exported', False):
                self.clear_emails()
        else:
            self.status_label.setText("Export failed")
            QMessageBox.critical(self, "Export Error", message)

    @pyqtSlot()
    def clear_emails(self):
        """Clear all email addresses"""
        reply = QMessageBox.question(
            self,
            "Clear Emails",
            "Are you sure you want to clear all email addresses?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Clear database
            self.db_manager.clear_all_emails()

            # Clear table
            self.table.setRowCount(0)

            # Update counts
            self.update_counts()

            self.status_label.setText("All emails cleared")

    @pyqtSlot()
    def delete_selected_emails(self):
        """Delete selected email addresses"""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.information(self, "No Selection", "No emails selected.")
            return

        reply = QMessageBox.question(
            self,
            "Delete Emails",
            f"Are you sure you want to delete {len(selected_rows)} selected email(s)?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Get emails to delete
            emails_to_delete = []
            for row in sorted(selected_rows, reverse=True):
                email_item = self.table.item(row, 0)
                if email_item:
                    emails_to_delete.append(email_item.text())

            # Delete from database
            for email in emails_to_delete:
                self.db_manager.delete_email(email)

            # Delete from table (in reverse order to avoid index issues)
            for row in sorted(selected_rows, reverse=True):
                self.table.removeRow(row)

            # Update counts
            self.update_counts()

            self.status_label.setText(f"Deleted {len(selected_rows)} email(s)")

    @pyqtSlot()
    def find_emails(self):
        """Find email addresses"""
        # TODO: Implement find functionality
        QMessageBox.information(self, "Not Implemented", "Find functionality is not implemented yet.")

    @pyqtSlot()
    def show_options(self):
        """Show options dialog"""
        dialog = OptionsDialog(self.config, self.db_manager, self)
        dialog.exec_()

    @pyqtSlot(bool)
    def toggle_single_mode(self, enabled):
        """Toggle single mode

        Args:
            enabled (bool): True if single mode is enabled, False otherwise
        """
        self.config.set('single_mode', enabled)

        # Update button text
        self.single_mode_button.setText("Enabled" if enabled else "Enable")

    @pyqtSlot(QPoint)
    def show_context_menu(self, pos):
        """Show context menu for table

        Args:
            pos (QPoint): Position where the context menu should be shown
        """
        # Get selected rows
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            return

        # Create context menu
        menu = QMenu(self)

        # Add actions
        check_action = menu.addAction("Check")
        check_action.triggered.connect(self.check_selected_emails)

        delete_action = menu.addAction("Delete")
        delete_action.triggered.connect(self.delete_selected_emails)

        # Show menu
        menu.exec_(self.table.mapToGlobal(pos))

    @pyqtSlot()
    def check_selected_emails(self):
        """Check selected email addresses"""
        # Get selected rows
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.information(self, "No Selection", "No emails selected.")
            return

        # Get emails to check
        emails = []
        for row in selected_rows:
            email_item = self.table.item(row, 0)
            name_item = self.table.item(row, 1)

            if email_item:
                email = email_item.text()
                name = name_item.text() if name_item else None

                emails.append({'email': email, 'name': name})

        # Show progress bar
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)

        # Start validation
        self.email_validator.start_validation(
            emails,
            on_result=self.on_validation_result,
            on_progress=self.on_validation_progress,
            on_finished=self.on_validation_finished
        )

        self.status_label.setText(f"Checking {len(emails)} selected email(s)...")

    def closeEvent(self, event):
        """Handle window close event

        Args:
            event: Close event
        """
        # Save window size
        self.config.set('window_width', self.width())
        self.config.set('window_height', self.height())

        # Accept the event
        event.accept()
