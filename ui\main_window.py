#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main Window for Email Checker Pro
"""

import os
import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QHeaderView, QToolBar, QAction, QStatusBar,
    QProgressBar, QLabel, QFileDialog, QMessageBox, QMenu, QSplitter,
    QFrame, QPushButton, QDialog, QGroupBox, QRadioButton, QSpinBox,
    QApplication, QShortcut, QDesktopWidget
)
from PyQt5.QtGui import QIcon, QColor, QPixmap, QKeySequence
from PyQt5.QtCore import Qt, QSize, pyqtSlot, QTimer, QPoint

from core.db_manager import DatabaseManager
from core.email_validator import EmailValidator
from core.import_export import ImportExportManager
from ui.options_dialog import OptionsDialog
from ui.import_dialog import ImportDialog
from config import Config

class MainWindow(QMainWindow):
    """Main window of the application"""

    def __init__(self):
        """Initialize the main window"""
        super().__init__()

        # Initialize components
        self.config = Config()
        self.db_manager = DatabaseManager()
        self.email_validator = EmailValidator(self.db_manager)
        self.import_export_manager = ImportExportManager(self.db_manager)

        # Set up the UI
        self.setup_ui()

        # Load emails from database
        self.load_emails()

        # Setup window properties and size controls
        self.setup_window_properties()

    def setup_ui(self):
        """Set up the user interface"""
        # Set window properties
        self.setWindowTitle("Email Checker Pro")
        self.resize(
            self.config.get('window_width', 1000),
            self.config.get('window_height', 700)
        )

        # Set main window style with professional gradient
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)

        # Create central widget
        central_widget = QWidget()
        central_widget.setStyleSheet("""
            QWidget {
                background: transparent;
            }
        """)
        self.setCentralWidget(central_widget)

        # Create main layout with professional spacing
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # Create splitter for resizable panels with professional styling
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #dee2e6, stop: 1 #adb5bd);
                width: 4px;
                border-radius: 2px;
                margin: 2px;
            }
            QSplitter::handle:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #6c757d, stop: 1 #495057);
            }
        """)
        main_layout.addWidget(splitter)

        # Create left panel (table) with professional styling
        left_panel = QWidget()
        left_panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e9ecef;
            }
        """)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(20, 20, 20, 20)

        # Create table with professional design
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "Email Address", "Name", "Result", "Description", "Suggestion", "Last Check"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.verticalHeader().setVisible(False)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.setAlternatingRowColors(True)
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        # Set professional table style
        self.table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #e9ecef;
                border-radius: 10px;
                background-color: #ffffff;
                gridline-color: #f1f3f4;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 10pt;
            }
            QTableWidget::item {
                padding: 10px 15px;
                border-bottom: 1px solid #f1f3f4;
                border-right: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                color: #1565c0;
                border: none;
            }
            QTableWidget::item:hover {
                background-color: #f5f5f5;
            }
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                padding: 12px 15px;
                border: 1px solid #dee2e6;
                border-bottom: 3px solid #6c757d;
                font-weight: 600;
                font-size: 10pt;
                color: #495057;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 14px;
                border-radius: 7px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #ced4da, stop: 1 #adb5bd);
                border-radius: 7px;
                min-height: 25px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #adb5bd, stop: 1 #6c757d);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        left_layout.addWidget(self.table)

        # Create right panel (stats and controls) with professional styling
        right_panel = QWidget()
        right_panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e9ecef;
            }
        """)
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(20, 20, 20, 20)
        right_layout.setSpacing(15)

        # Create counts group with professional design
        counts_group = QGroupBox("📊 Statistics")
        counts_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                margin-top: 15px;
                font-weight: 600;
                font-size: 11pt;
                color: #495057;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        counts_layout = QVBoxLayout(counts_group)
        counts_layout.setContentsMargins(15, 20, 15, 15)
        counts_layout.setSpacing(8)

        self.counts_labels = {
            'All': QLabel("0"),
            'Wait': QLabel("0"),
            'OK': QLabel("0"),
            'BAD': QLabel("0"),
            'Server Not Found': QLabel("0"),
            'Checking Failed': QLabel("0")
        }

        # Professional status icons and colors
        status_icons = {
            'All': '📧',
            'Wait': '⏳',
            'OK': '✅',
            'BAD': '❌',
            'Server Not Found': '🔍',
            'Checking Failed': '⚠️'
        }

        for status, label in self.counts_labels.items():
            row_layout = QHBoxLayout()
            row_layout.setContentsMargins(5, 3, 5, 3)

            # Create status label with icon
            icon = status_icons.get(status, '📧')
            status_label = QLabel(f"{icon} {status}")

            # Set professional status colors and styles
            if status == 'Wait':
                status_label.setStyleSheet("color: #2196F3; font-weight: 600; font-size: 10pt;")
                label.setStyleSheet("color: #2196F3; font-weight: 700; font-size: 11pt; background: #E3F2FD; padding: 3px 8px; border-radius: 4px;")
            elif status == 'OK':
                status_label.setStyleSheet("color: #4CAF50; font-weight: 600; font-size: 10pt;")
                label.setStyleSheet("color: #4CAF50; font-weight: 700; font-size: 11pt; background: #E8F5E8; padding: 3px 8px; border-radius: 4px;")
            elif status == 'BAD':
                status_label.setStyleSheet("color: #F44336; font-weight: 600; font-size: 10pt;")
                label.setStyleSheet("color: #F44336; font-weight: 700; font-size: 11pt; background: #FFEBEE; padding: 3px 8px; border-radius: 4px;")
            elif status == 'Server Not Found':
                status_label.setStyleSheet("color: #FF9800; font-weight: 600; font-size: 10pt;")
                label.setStyleSheet("color: #FF9800; font-weight: 700; font-size: 11pt; background: #FFF3E0; padding: 3px 8px; border-radius: 4px;")
            elif status == 'Checking Failed':
                status_label.setStyleSheet("color: #9E9E9E; font-weight: 600; font-size: 10pt;")
                label.setStyleSheet("color: #9E9E9E; font-weight: 700; font-size: 11pt; background: #F5F5F5; padding: 3px 8px; border-radius: 4px;")
            else:
                status_label.setStyleSheet("color: #6C757D; font-weight: 600; font-size: 10pt;")
                label.setStyleSheet("color: #6C757D; font-weight: 700; font-size: 11pt; background: #F8F9FA; padding: 3px 8px; border-radius: 4px;")

            row_layout.addWidget(status_label)
            row_layout.addStretch()
            row_layout.addWidget(label)
            counts_layout.addLayout(row_layout)

        right_layout.addWidget(counts_group)

        # Create pie chart group with professional design
        pie_chart_group = QGroupBox("📈 3D Pie Chart")
        pie_chart_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                margin-top: 15px;
                font-weight: 600;
                font-size: 11pt;
                color: #495057;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        pie_chart_layout = QVBoxLayout(pie_chart_group)
        pie_chart_layout.setContentsMargins(15, 20, 15, 15)

        # Add pie chart widget
        from ui.pie_chart import PieChartWidget
        self.pie_chart = PieChartWidget()
        pie_chart_layout.addWidget(self.pie_chart)

        right_layout.addWidget(pie_chart_group)

        # Create single mode group with professional design
        single_mode_group = QGroupBox("⚡ Single Mode")
        single_mode_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                margin-top: 15px;
                font-weight: 600;
                font-size: 11pt;
                color: #495057;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        single_mode_layout = QVBoxLayout(single_mode_group)
        single_mode_layout.setContentsMargins(15, 20, 15, 15)

        self.single_mode_button = QPushButton("🔄 Enable")
        self.single_mode_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 10px 15px;
                font-weight: 600;
                font-size: 10pt;
                color: #495057;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                border: 2px solid #2196F3;
                color: #1565c0;
            }
            QPushButton:checked {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50, stop: 1 #66BB6A);
                border: 2px solid #388E3C;
                color: white;
            }
        """)
        self.single_mode_button.setCheckable(True)
        self.single_mode_button.setChecked(self.config.get('single_mode', False))
        self.single_mode_button.toggled.connect(self.toggle_single_mode)
        single_mode_layout.addWidget(self.single_mode_button)

        right_layout.addWidget(single_mode_group)

        # Create tip group with professional design
        tip_group = QGroupBox("💡 Pro Tip")
        tip_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                margin-top: 15px;
                font-weight: 600;
                font-size: 11pt;
                color: #495057;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        tip_layout = QVBoxLayout(tip_group)
        tip_layout.setContentsMargins(15, 20, 15, 15)

        tip_label = QLabel("If you always get the 'Recheck' suggestion for an item, we recommend that you discard this email address.")
        tip_label.setWordWrap(True)
        tip_label.setStyleSheet("""
            QLabel {
                font-style: italic;
                color: #6C757D;
                font-size: 9pt;
                line-height: 1.4;
                padding: 5px;
                background: #FFF3CD;
                border: 1px solid #FFEAA7;
                border-radius: 6px;
            }
        """)
        tip_layout.addWidget(tip_label)

        right_layout.addWidget(tip_group)

        # Add stretch to push everything to the top
        right_layout.addStretch()

        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)

        # Set initial splitter sizes (65% left, 35% right) for better balance
        splitter.setSizes([int(self.width() * 0.65), int(self.width() * 0.35)])

        # Create toolbar
        self.create_toolbar()

        # Create menu bar
        self.create_menu_bar()

        # Create professional status bar
        self.statusBar = QStatusBar()
        self.statusBar.setStyleSheet("""
            QStatusBar {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border-top: 2px solid #dee2e6;
                color: #495057;
                font-weight: 500;
                padding: 5px;
            }
            QStatusBar::item {
                border: none;
            }
        """)
        self.setStatusBar(self.statusBar)

        # Add professional progress bar to status bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(250)
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: #f8f9fa;
                text-align: center;
                font-weight: 600;
                color: #495057;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #4CAF50, stop: 1 #66BB6A);
                border-radius: 6px;
                margin: 1px;
            }
        """)
        self.statusBar.addPermanentWidget(self.progress_bar)

        # Add professional status label to status bar
        self.status_label = QLabel("🚀 Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-weight: 600;
                font-size: 10pt;
                padding: 5px 10px;
            }
        """)
        self.statusBar.addWidget(self.status_label)

    def create_toolbar(self):
        """Create the toolbar with professional styling"""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setIconSize(QSize(36, 36))
        toolbar.setMovable(False)

        # Set professional toolbar style
        toolbar.setStyleSheet("""
            QToolBar {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: none;
                border-bottom: 3px solid #dee2e6;
                spacing: 8px;
                padding: 8px;
            }
            QToolButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
                font-weight: 600;
                font-size: 9pt;
                color: #495057;
            }
            QToolButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                border: 2px solid #2196F3;
                color: #1565c0;
            }
            QToolButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #bbdefb, stop: 1 #90caf9);
                border: 2px solid #1976d2;
            }
        """)

        self.addToolBar(toolbar)

        # Get the absolute path to the icons directory
        icons_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'resources', 'icons'))

        # Check action
        check_icon_path = os.path.join(icons_dir, 'check.png')
        if os.path.exists(check_icon_path):
            check_action = QAction(QIcon(check_icon_path), "Check", self)
        else:
            check_action = QAction("✅ Check", self)
        check_action.setStatusTip("Check email addresses")
        check_action.triggered.connect(self.check_emails)
        toolbar.addAction(check_action)

        # Import action
        import_icon_path = os.path.join(icons_dir, 'import.png')
        if os.path.exists(import_icon_path):
            import_action = QAction(QIcon(import_icon_path), "Import", self)
        else:
            import_action = QAction("📥 Import", self)
        import_action.setStatusTip("Import email addresses")
        import_action.triggered.connect(self.import_emails)
        toolbar.addAction(import_action)

        # Export action
        export_icon_path = os.path.join(icons_dir, 'export.png')
        if os.path.exists(export_icon_path):
            export_action = QAction(QIcon(export_icon_path), "Export", self)
        else:
            export_action = QAction("📤 Export", self)
        export_action.setStatusTip("Export email addresses")
        export_action.triggered.connect(self.export_emails)
        toolbar.addAction(export_action)

        # Clear action
        clear_icon_path = os.path.join(icons_dir, 'clear.png')
        if os.path.exists(clear_icon_path):
            clear_action = QAction(QIcon(clear_icon_path), "Clear", self)
        else:
            clear_action = QAction("🗑️ Clear", self)
        clear_action.setStatusTip("Clear all email addresses")
        clear_action.triggered.connect(self.clear_emails)
        toolbar.addAction(clear_action)

        # Delete action
        delete_icon_path = os.path.join(icons_dir, 'delete.png')
        if os.path.exists(delete_icon_path):
            delete_action = QAction(QIcon(delete_icon_path), "Delete", self)
        else:
            delete_action = QAction("❌ Delete", self)
        delete_action.setStatusTip("Delete selected email addresses")
        delete_action.triggered.connect(self.delete_selected_emails)
        toolbar.addAction(delete_action)

        # Find action
        find_icon_path = os.path.join(icons_dir, 'find.png')
        if os.path.exists(find_icon_path):
            find_action = QAction(QIcon(find_icon_path), "Find", self)
        else:
            find_action = QAction("🔍 Find", self)
        find_action.setStatusTip("Find email addresses")
        find_action.triggered.connect(self.find_emails)
        toolbar.addAction(find_action)

        # Options action
        options_icon_path = os.path.join(icons_dir, 'options.png')
        if os.path.exists(options_icon_path):
            options_action = QAction(QIcon(options_icon_path), "Options", self)
        else:
            options_action = QAction("⚙️ Options", self)
        options_action.setStatusTip("Configure options")
        options_action.triggered.connect(self.show_options)
        toolbar.addAction(options_action)

        # Add text under icons
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)

    def create_menu_bar(self):
        """Create the menu bar with View menu for window controls"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border-bottom: 2px solid #dee2e6;
                color: #495057;
                font-weight: 600;
                font-size: 10pt;
                padding: 5px;
            }
            QMenuBar::item {
                background: transparent;
                padding: 8px 15px;
                border-radius: 5px;
                margin: 2px;
            }
            QMenuBar::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                color: #1565c0;
            }
            QMenu {
                background: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 5px;
                color: #495057;
                font-size: 10pt;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 5px;
                margin: 1px;
            }
            QMenu::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                color: #1565c0;
            }
            QMenu::separator {
                height: 2px;
                background: #dee2e6;
                margin: 5px 10px;
            }
        """)

        # View menu
        view_menu = menubar.addMenu('📋 View')

        # Window size submenu
        size_menu = view_menu.addMenu('📏 Window Size')

        # Zoom in action
        zoom_in_action = QAction('🔍 Zoom In (Ctrl++)', self)
        zoom_in_action.setShortcut('Ctrl++')
        zoom_in_action.triggered.connect(self.zoom_in)
        size_menu.addAction(zoom_in_action)

        # Zoom out action
        zoom_out_action = QAction('🔍 Zoom Out (Ctrl+-)', self)
        zoom_out_action.setShortcut('Ctrl+-')
        zoom_out_action.triggered.connect(self.zoom_out)
        size_menu.addAction(zoom_out_action)

        size_menu.addSeparator()

        # Reset size action
        reset_action = QAction('🔄 Reset Size (Ctrl+0)', self)
        reset_action.setShortcut('Ctrl+0')
        reset_action.triggered.connect(self.reset_window_size)
        size_menu.addAction(reset_action)

        size_menu.addSeparator()

        # Predefined sizes
        small_action = QAction('📱 Small (800x600)', self)
        small_action.triggered.connect(lambda: self.set_window_size(800, 600))
        size_menu.addAction(small_action)

        medium_action = QAction('💻 Medium (1000x700)', self)
        medium_action.triggered.connect(lambda: self.set_window_size(1000, 700))
        size_menu.addAction(medium_action)

        large_action = QAction('🖥️ Large (1200x800)', self)
        large_action.triggered.connect(lambda: self.set_window_size(1200, 800))
        size_menu.addAction(large_action)

        xlarge_action = QAction('📺 Extra Large (1400x900)', self)
        xlarge_action.triggered.connect(lambda: self.set_window_size(1400, 900))
        size_menu.addAction(xlarge_action)

        view_menu.addSeparator()

        # Fullscreen action
        fullscreen_action = QAction('🖥️ Toggle Fullscreen (F11)', self)
        fullscreen_action.setShortcut('F11')
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # Center window action
        center_action = QAction('🎯 Center Window', self)
        center_action.triggered.connect(self.center_window)
        view_menu.addAction(center_action)

    def load_emails(self):
        """Load emails from the database"""
        emails = self.db_manager.get_all_emails()

        # Clear table
        self.table.setRowCount(0)

        # Add emails to table
        for email_data in emails:
            self.add_email_to_table(email_data)

        # Update counts
        self.update_counts()

    def add_email_to_table(self, email_data):
        """Add an email to the table

        Args:
            email_data (dict): Email data dictionary
        """
        try:
            row = self.table.rowCount()
            self.table.insertRow(row)

            # Set email address with icon
            email = email_data.get('email', '')
            result = email_data.get('result', 'Wait')

            email_item = QTableWidgetItem(email)

            try:
                # Set icon based on result
                icons_dir = os.path.join(os.path.dirname(__file__), '..', 'resources', 'icons')
                if result == 'OK':
                    icon_path = os.path.join(icons_dir, 'ok.png')
                    if os.path.exists(icon_path):
                        email_item.setIcon(QIcon(icon_path))
                        # Make the icon more visible
                        email_item.setData(Qt.DecorationRole, QIcon(icon_path))
                elif result == 'BAD':
                    icon_path = os.path.join(icons_dir, 'bad.png')
                    if os.path.exists(icon_path):
                        email_item.setIcon(QIcon(icon_path))
                        # Make the icon more visible
                        email_item.setData(Qt.DecorationRole, QIcon(icon_path))
                elif result == 'Server Not Found' or result == 'Checking Failed':
                    icon_path = os.path.join(icons_dir, 'warning.png')
                    if os.path.exists(icon_path):
                        email_item.setIcon(QIcon(icon_path))
                        # Make the icon more visible
                        email_item.setData(Qt.DecorationRole, QIcon(icon_path))
            except Exception as e:
                print(f"Error setting icon: {str(e)}")

            self.table.setItem(row, 0, email_item)

            # Set name
            try:
                name_item = QTableWidgetItem(email_data.get('name', ''))
                self.table.setItem(row, 1, name_item)
            except Exception as e:
                print(f"Error setting name: {str(e)}")
                self.table.setItem(row, 1, QTableWidgetItem(''))

            # Set result
            try:
                result_item = QTableWidgetItem(result)

                # Set result color and font
                font = result_item.font()
                font.setBold(True)
                result_item.setFont(font)

                if result == 'Wait':
                    result_item.setForeground(QColor('#2196F3'))  # Material Blue
                elif result == 'OK':
                    result_item.setForeground(QColor('#4CAF50'))  # Material Green
                elif result == 'BAD':
                    result_item.setForeground(QColor('#F44336'))  # Material Red
                elif result == 'Server Not Found':
                    result_item.setForeground(QColor('#FF9800'))  # Material Orange
                elif result == 'Checking Failed':
                    result_item.setForeground(QColor('#9E9E9E'))  # Material Grey

                self.table.setItem(row, 2, result_item)
            except Exception as e:
                print(f"Error setting result: {str(e)}")
                self.table.setItem(row, 2, QTableWidgetItem('Wait'))

            # Set description
            try:
                description = email_data.get('description', 'Wait for Checking')
                description_item = QTableWidgetItem(description)

                # Set description color based on result
                if result == 'OK':
                    description_item.setForeground(QColor('#4CAF50'))  # Material Green
                elif result == 'BAD':
                    description_item.setForeground(QColor('#F44336'))  # Material Red
                    # Make text italic for BAD results
                    font = description_item.font()
                    font.setItalic(True)
                    description_item.setFont(font)
                elif result == 'Server Not Found':
                    description_item.setForeground(QColor('#FF9800'))  # Material Orange
                elif result == 'Checking Failed':
                    description_item.setForeground(QColor('#9E9E9E'))  # Material Grey

                self.table.setItem(row, 3, description_item)
            except Exception as e:
                print(f"Error setting description: {str(e)}")
                self.table.setItem(row, 3, QTableWidgetItem('Wait for Checking'))

            # Set suggestion
            try:
                suggestion = email_data.get('suggestion', 'N/A')
                suggestion_item = QTableWidgetItem(suggestion)

                # Set suggestion color and font
                font = suggestion_item.font()
                font.setBold(True)
                suggestion_item.setFont(font)

                if suggestion == 'Retain':
                    suggestion_item.setForeground(QColor('#4CAF50'))  # Material Green
                elif suggestion == 'Discard':
                    suggestion_item.setForeground(QColor('#F44336'))  # Material Red

                self.table.setItem(row, 4, suggestion_item)
            except Exception as e:
                print(f"Error setting suggestion: {str(e)}")
                self.table.setItem(row, 4, QTableWidgetItem('N/A'))

            # Set last check
            try:
                from datetime import datetime
                last_check = email_data.get('last_check', None)
                if last_check:
                    if isinstance(last_check, str):
                        last_check_text = last_check
                    else:
                        last_check_text = last_check.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    last_check_text = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                last_check_item = QTableWidgetItem(last_check_text)

                # Set font for last check
                font = last_check_item.font()
                font.setPointSize(8)  # Smaller font
                last_check_item.setFont(font)

                self.table.setItem(row, 5, last_check_item)
            except Exception as e:
                print(f"Error setting last check: {str(e)}")
                self.table.setItem(row, 5, QTableWidgetItem('N/A'))

        except Exception as e:
            print(f"Unexpected error in add_email_to_table: {str(e)}")

    def update_counts(self):
        """Update the counts in the right panel"""
        counts = {
            'All': 0,
            'Wait': 0,
            'OK': 0,
            'BAD': 0,
            'Server Not Found': 0,
            'Checking Failed': 0
        }

        # Count emails by result
        for row in range(self.table.rowCount()):
            result_item = self.table.item(row, 2)
            result = result_item.text() if result_item else 'Wait'

            counts['All'] += 1
            if result in counts:
                counts[result] += 1

        # Update labels
        for status, count in counts.items():
            if status in self.counts_labels:
                self.counts_labels[status].setText(str(count))

        # Update pie chart
        pie_data = {
            'Wait': counts['Wait'],
            'OK': counts['OK'],
            'BAD': counts['BAD'],
            'Server Not Found': counts['Server Not Found'],
            'Checking Failed': counts['Checking Failed']
        }
        self.pie_chart.set_data(pie_data)

    @pyqtSlot()
    def check_emails(self):
        """Check email addresses"""
        # Get emails from table
        emails = []
        for row in range(self.table.rowCount()):
            email_item = self.table.item(row, 0)
            name_item = self.table.item(row, 1)
            result_item = self.table.item(row, 2)

            if email_item:
                email = email_item.text()
                name = name_item.text() if name_item else None
                result = result_item.text() if result_item else 'Wait'

                # Check if we should recheck this email
                check_history = self.config.get('check_history', 'recheck_if_not_ok')

                if check_history == 'use_old' and result != 'Wait':
                    # Skip this email, use old result
                    continue
                elif check_history == 'always_recheck':
                    # Always recheck
                    pass
                elif check_history == 'recheck_if_not_ok' and result == 'OK':
                    # Skip if result is OK
                    continue
                elif check_history == 'recheck_if_recheck':
                    # TODO: Implement recheck suggestion logic
                    pass
                elif check_history == 'recheck_if_old':
                    # TODO: Implement old check date logic
                    pass

                emails.append({'email': email, 'name': name})

        if not emails:
            QMessageBox.information(self, "No Emails", "No emails to check.")
            return

        # Show progress bar
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)

        # Show checking message in the status bar
        self.status_label.setText("Now checking current list, please wait...")

        # Create a temporary row for the message if table is empty
        if self.table.rowCount() == 0:
            self.table.insertRow(0)
            for col in range(self.table.columnCount()):
                self.table.setItem(0, col, QTableWidgetItem(""))

            message_item = QTableWidgetItem("Now checking current list, please wait...")
            message_item.setTextAlignment(Qt.AlignCenter)
            message_item.setBackground(QColor(51, 102, 153))  # Navy blue background
            message_item.setForeground(QColor(255, 255, 255))  # White text
            font = message_item.font()
            font.setBold(True)
            message_item.setFont(font)
            self.table.setItem(0, 0, message_item)

        # Process events to update UI
        QApplication.processEvents()

        # Start validation
        self.email_validator.start_validation(
            emails,
            on_result=self.on_validation_result,
            on_progress=self.on_validation_progress,
            on_finished=self.on_validation_finished
        )

        self.status_label.setText(f"Checking {len(emails)} email(s)...")

    @pyqtSlot(str, str, str, str)
    def on_validation_result(self, email, result, description, suggestion):
        """Handle validation result

        Args:
            email (str): Email address
            result (str): Validation result
            description (str): Detailed description
            suggestion (str): Suggestion for the user
        """
        # Update database
        self.db_manager.update_email_result(email, result, description, suggestion)

        # Update table
        for row in range(self.table.rowCount()):
            email_item = self.table.item(row, 0)

            if email_item and email_item.text() == email:
                # Update result
                result_item = QTableWidgetItem(result)

                # Set result color
                if result == 'Wait':
                    result_item.setForeground(QColor('blue'))
                elif result == 'OK':
                    result_item.setForeground(QColor('green'))
                elif result == 'BAD':
                    result_item.setForeground(QColor('red'))
                elif result == 'Server Not Found':
                    result_item.setForeground(QColor('orange'))
                elif result == 'Checking Failed':
                    result_item.setForeground(QColor('gray'))

                self.table.setItem(row, 2, result_item)

                # Update description
                description_item = QTableWidgetItem(description)
                self.table.setItem(row, 3, description_item)

                # Update suggestion
                suggestion_item = QTableWidgetItem(suggestion or 'N/A')
                self.table.setItem(row, 4, suggestion_item)

                # Update last check
                from datetime import datetime
                last_check_item = QTableWidgetItem(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                self.table.setItem(row, 5, last_check_item)

                break

        # Update counts
        self.update_counts()

        # Process events to update UI
        QApplication.processEvents()

    @pyqtSlot(int)
    def on_validation_progress(self, progress):
        """Handle validation progress

        Args:
            progress (int): Progress percentage (0-100)
        """
        self.progress_bar.setValue(progress)

    @pyqtSlot()
    def on_validation_finished(self):
        """Handle validation finished"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Email checking completed")

        # Remove any span in the table
        for row in range(self.table.rowCount()):
            for col in range(self.table.columnCount()):
                if self.table.rowSpan(row, col) > 1 or self.table.columnSpan(row, col) > 1:
                    self.table.setSpan(row, col, 1, 1)

        # Update counts
        self.update_counts()

    @pyqtSlot()
    def import_emails(self):
        """Import email addresses from a file"""
        # Show import dialog
        dialog = ImportDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            file_path = dialog.get_file_path()

            if file_path:
                # Show progress bar
                self.progress_bar.setValue(0)
                self.progress_bar.setVisible(True)

                # Start import
                self.import_export_manager.start_import(
                    file_path,
                    on_email_found=self.on_email_imported,
                    on_progress=self.on_import_progress,
                    on_finished=self.on_import_finished,
                    on_error=self.on_import_error
                )

                self.status_label.setText(f"Importing emails from {os.path.basename(file_path)}...")

    @pyqtSlot(str, str)
    def on_email_imported(self, email, name):
        """Handle imported email

        Args:
            email (str): Email address
            name (str): Name associated with the email
        """
        try:
            # Add to database
            try:
                self.db_manager.add_email(email, name)
            except Exception as e:
                print(f"Error adding email to database: {str(e)}")
                # Continue anyway to add to table

            # Add to table
            try:
                self.add_email_to_table({
                    'email': email,
                    'name': name,
                    'result': 'Wait',
                    'description': 'Wait for Checking',
                    'suggestion': 'N/A',
                    'last_check': 'N/A'
                })
            except Exception as e:
                print(f"Error adding email to table: {str(e)}")
        except Exception as e:
            print(f"Unexpected error in on_email_imported: {str(e)}")

        # Update counts
        self.update_counts()

    @pyqtSlot(int)
    def on_import_progress(self, progress):
        """Handle import progress

        Args:
            progress (int): Progress percentage (0-100)
        """
        self.progress_bar.setValue(progress)

    @pyqtSlot(int)
    def on_import_finished(self, count):
        """Handle import finished

        Args:
            count (int): Number of imported emails
        """
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"Imported {count} email(s)")

        # Update counts
        self.update_counts()

        # Ask if user wants to check the imported emails
        if count > 0:
            reply = QMessageBox.question(
                self,
                "Check Emails",
                f"Do you want to check the {count} imported email(s) now?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.check_emails()

    @pyqtSlot(str)
    def on_import_error(self, error):
        """Handle import error

        Args:
            error (str): Error message
        """
        self.progress_bar.setVisible(False)
        self.status_label.setText("Import failed")

        QMessageBox.critical(self, "Import Error", error)

    @pyqtSlot()
    def export_emails(self):
        """Export email addresses to a file"""
        # Get export options
        export_columns = self.config.get('export_columns', ['Name', 'Email Address', 'Result', 'Description', 'Suggestion', 'Last Check'])

        # TODO: Show export dialog

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Emails",
            "",
            "CSV Files (*.csv);;Text Files (*.txt);;Excel Files (*.xlsx)"
        )

        if not file_path:
            return

        # Get emails from table
        emails = []
        for row in range(self.table.rowCount()):
            email_data = {}

            for col, header in enumerate(['Email Address', 'Name', 'Result', 'Description', 'Suggestion', 'Last Check']):
                item = self.table.item(row, col)
                email_data[header] = item.text() if item else ''

            emails.append(email_data)

        if not emails:
            QMessageBox.information(self, "No Emails", "No emails to export.")
            return

        # Show progress bar
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)

        # Start export
        self.import_export_manager.start_export(
            file_path,
            emails,
            export_columns,
            on_progress=self.on_export_progress,
            on_finished=self.on_export_finished
        )

        self.status_label.setText(f"Exporting {len(emails)} email(s) to {os.path.basename(file_path)}...")

    @pyqtSlot(int)
    def on_export_progress(self, progress):
        """Handle export progress

        Args:
            progress (int): Progress percentage (0-100)
        """
        self.progress_bar.setValue(progress)

    @pyqtSlot(bool, str)
    def on_export_finished(self, success, message):
        """Handle export finished

        Args:
            success (bool): True if export was successful, False otherwise
            message (str): Success or error message
        """
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("Export completed")
            QMessageBox.information(self, "Export Completed", message)

            # Remove exported emails if configured
            if self.config.get('remove_exported', False):
                self.clear_emails()
        else:
            self.status_label.setText("Export failed")
            QMessageBox.critical(self, "Export Error", message)

    @pyqtSlot()
    def clear_emails(self):
        """Clear all email addresses"""
        reply = QMessageBox.question(
            self,
            "Clear Emails",
            "Are you sure you want to clear all email addresses?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Clear database
            self.db_manager.clear_all_emails()

            # Clear table
            self.table.setRowCount(0)

            # Update counts
            self.update_counts()

            self.status_label.setText("All emails cleared")

    @pyqtSlot()
    def delete_selected_emails(self):
        """Delete selected email addresses"""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.information(self, "No Selection", "No emails selected.")
            return

        reply = QMessageBox.question(
            self,
            "Delete Emails",
            f"Are you sure you want to delete {len(selected_rows)} selected email(s)?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Get emails to delete
            emails_to_delete = []
            for row in sorted(selected_rows, reverse=True):
                email_item = self.table.item(row, 0)
                if email_item:
                    emails_to_delete.append(email_item.text())

            # Delete from database
            for email in emails_to_delete:
                self.db_manager.delete_email(email)

            # Delete from table (in reverse order to avoid index issues)
            for row in sorted(selected_rows, reverse=True):
                self.table.removeRow(row)

            # Update counts
            self.update_counts()

            self.status_label.setText(f"Deleted {len(selected_rows)} email(s)")

    @pyqtSlot()
    def find_emails(self):
        """Find email addresses"""
        # TODO: Implement find functionality
        QMessageBox.information(self, "Not Implemented", "Find functionality is not implemented yet.")

    @pyqtSlot()
    def show_options(self):
        """Show options dialog"""
        dialog = OptionsDialog(self.config, self.db_manager, self)
        dialog.exec_()

    @pyqtSlot(bool)
    def toggle_single_mode(self, enabled):
        """Toggle single mode

        Args:
            enabled (bool): True if single mode is enabled, False otherwise
        """
        self.config.set('single_mode', enabled)

        # Update button text
        self.single_mode_button.setText("Enabled" if enabled else "Enable")

    @pyqtSlot(QPoint)
    def show_context_menu(self, pos):
        """Show context menu for table

        Args:
            pos (QPoint): Position where the context menu should be shown
        """
        # Get selected rows
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            return

        # Create context menu
        menu = QMenu(self)

        # Add actions
        check_action = menu.addAction("Check")
        check_action.triggered.connect(self.check_selected_emails)

        delete_action = menu.addAction("Delete")
        delete_action.triggered.connect(self.delete_selected_emails)

        # Show menu
        menu.exec_(self.table.mapToGlobal(pos))

    @pyqtSlot()
    def check_selected_emails(self):
        """Check selected email addresses"""
        # Get selected rows
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())

        if not selected_rows:
            QMessageBox.information(self, "No Selection", "No emails selected.")
            return

        # Get emails to check
        emails = []
        for row in selected_rows:
            email_item = self.table.item(row, 0)
            name_item = self.table.item(row, 1)

            if email_item:
                email = email_item.text()
                name = name_item.text() if name_item else None

                emails.append({'email': email, 'name': name})

        # Show progress bar
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)

        # Start validation
        self.email_validator.start_validation(
            emails,
            on_result=self.on_validation_result,
            on_progress=self.on_validation_progress,
            on_finished=self.on_validation_finished
        )

        self.status_label.setText(f"Checking {len(emails)} selected email(s)...")

    def setup_window_properties(self):
        """Setup window properties and size controls"""
        # Set minimum and maximum window sizes
        self.setMinimumSize(800, 600)
        self.setMaximumSize(2000, 1500)

        # Enable window resizing
        self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint | Qt.WindowMinimizeButtonHint)

        # Set window icon
        icon_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'icons', 'app_icon.png')
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # Center window on screen
        self.center_window()

        # Add keyboard shortcuts for window control
        # Ctrl+Plus for zoom in (increase size)
        zoom_in_shortcut = QShortcut(QKeySequence("Ctrl++"), self)
        zoom_in_shortcut.activated.connect(self.zoom_in)

        # Ctrl+Minus for zoom out (decrease size)
        zoom_out_shortcut = QShortcut(QKeySequence("Ctrl+-"), self)
        zoom_out_shortcut.activated.connect(self.zoom_out)

        # Ctrl+0 for reset to default size
        reset_size_shortcut = QShortcut(QKeySequence("Ctrl+0"), self)
        reset_size_shortcut.activated.connect(self.reset_window_size)

        # F11 for fullscreen toggle
        fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        fullscreen_shortcut.activated.connect(self.toggle_fullscreen)

    def center_window(self):
        """Center the window on the screen"""
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def zoom_in(self):
        """Increase window size by 10%"""
        current_width = self.width()
        current_height = self.height()
        new_width = min(int(current_width * 1.1), self.maximumWidth())
        new_height = min(int(current_height * 1.1), self.maximumHeight())
        self.resize(new_width, new_height)
        self.center_window()
        self.status_label.setText(f"🔍 Zoomed In - Size: {new_width}x{new_height}")

    def zoom_out(self):
        """Decrease window size by 10%"""
        current_width = self.width()
        current_height = self.height()
        new_width = max(int(current_width * 0.9), self.minimumWidth())
        new_height = max(int(current_height * 0.9), self.minimumHeight())
        self.resize(new_width, new_height)
        self.center_window()
        self.status_label.setText(f"🔍 Zoomed Out - Size: {new_width}x{new_height}")

    def reset_window_size(self):
        """Reset window to default size"""
        default_width = 1000
        default_height = 700
        self.resize(default_width, default_height)
        self.center_window()
        self.status_label.setText(f"🔄 Reset Size - Size: {default_width}x{default_height}")

    def set_window_size(self, width, height):
        """Set window to specific size"""
        # Ensure size is within limits
        width = max(self.minimumWidth(), min(width, self.maximumWidth()))
        height = max(self.minimumHeight(), min(height, self.maximumHeight()))

        self.resize(width, height)
        self.center_window()
        self.status_label.setText(f"📐 Size Set - Size: {width}x{height}")

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        if self.isFullScreen():
            self.showNormal()
            self.status_label.setText("🪟 Windowed Mode")
        else:
            self.showFullScreen()
            self.status_label.setText("🖥️ Fullscreen Mode")

    def closeEvent(self, event):
        """Handle window close event

        Args:
            event: Close event
        """
        # Save window size
        self.config.set('window_width', self.width())
        self.config.set('window_height', self.height())

        # Accept the event
        event.accept()
