# Email Checker Pro

A professional email verification tool that validates email addresses and checks their deliverability.

## Features

- Import email addresses from various file formats (CSV, TXT, XLS, XLSX)
- Validate email addresses using multiple techniques:
  - Syntax validation
  - Domain validation (MX record check)
  - SMTP server validation
- Export validation results to various file formats
- Database integration for storing results
- Professional user interface

## Requirements

- Python 3.6+
- PyQt5
- email-validator
- pandas
- openpyxl
- dnspython

## Installation

1. Clone the repository
2. Install the required packages:

```bash
pip install -r requirements.txt
```

## Usage

Run the application:

```bash
python main.py
```

### Importing Emails

1. Click the "Import" button on the toolbar
2. Select the file format (CSV, TXT, XLS, XLSX)
3. Browse for the file
4. Click "OK" to import the emails

### Checking Emails

1. Click the "Check" button on the toolbar to check all emails
2. Or select specific emails and use the context menu to check only those

### Exporting Results

1. Click the "Export" button on the toolbar
2. Select the file format (CSV, TXT, XLSX)
3. Choose which columns to export
4. Click "OK" to export the results

## Configuration

Use the "Options" dialog to configure:

- Check history behavior
- Maximum number of checking threads
- Import/export options
- Unsupported email servers

## License

This project is licensed under the MIT License - see the LICENSE file for details.
