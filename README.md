# Email Checker Pro 🚀

A **professional email verification tool** with a modern, intuitive interface that validates email addresses and checks their deliverability with advanced features and beautiful design.

## ✨ Features

### 📧 Email Validation
- Import email addresses from various file formats (CSV, TXT, XLS, XLSX)
- Advanced email validation using multiple techniques:
  - **Syntax validation** - Check email format compliance
  - **Domain validation** - MX record verification
  - **SMTP server validation** - Real-time deliverability check
- Export validation results to multiple formats

### 🎨 Professional Interface
- **Modern Material Design** - Clean, professional appearance
- **3D Pie Chart** - Visual statistics with professional styling
- **Real-time Statistics** - Live count updates with color-coded status
- **Professional Toolbar** - Intuitive icons with hover effects
- **Responsive Layout** - Resizable panels for optimal workflow

### 🔧 Advanced Features
- **Database Integration** - SQLite storage for persistent results
- **Multi-threading** - Fast parallel email checking
- **Smart Rechecking** - Configurable recheck policies
- **Single Mode** - Focus on individual email validation
- **Professional Status Bar** - Progress tracking with modern design

## Requirements

- Python 3.6+
- PyQt5
- email-validator
- pandas
- openpyxl
- dnspython

## Installation

1. Clone the repository
2. Install the required packages:

```bash
pip install -r requirements.txt
```

## 🚀 Quick Start

### Running the Application

```bash
# Method 1: Direct run (recommended)
python run.py

# Method 2: Main script
python main.py
```

The `run.py` script automatically checks and installs dependencies if needed.

## 📖 Usage Guide

### 📥 Importing Emails

1. Click the **"Import"** button on the professional toolbar
2. Select your file format (CSV, TXT, XLS, XLSX)
3. Browse and select your email file
4. Watch the real-time import progress
5. View imported emails in the professional table

### ✅ Checking Emails

1. Click the **"Check"** button to validate all emails
2. Watch the beautiful progress bar and live statistics
3. See results update in real-time with color-coded status
4. View the 3D pie chart for visual statistics

### 📤 Exporting Results

1. Click the **"Export"** button on the toolbar
2. Choose your preferred format (CSV, TXT, XLSX)
3. Select which columns to include
4. Save your professional report

### 🎯 Professional Features

- **Live Statistics**: Real-time count updates with emoji icons
- **3D Visualization**: Professional pie chart with legend
- **Smart Filtering**: Context menu for advanced operations
- **Single Mode**: Toggle for focused email validation

## ⚙️ Configuration

Access the **"Options"** dialog to customize:

### 🔄 Check Behavior
- **Check History**: How to handle previously checked emails
- **Recheck Days**: Age threshold for rechecking emails
- **Max Threads**: Parallel processing optimization

### 📁 Import/Export Settings
- **File Format Support**: CSV, TXT, XLS, XLSX
- **Column Selection**: Choose which data to export
- **Sample Files**: Enable/disable sample file display

### 🚫 Server Management
- **Unsupported Servers**: Configure problematic email providers
- **SMTP Timeout**: Connection timeout settings

## 🎨 Design Highlights

### Modern Material Design
- **Professional Color Scheme**: Material Design colors throughout
- **Gradient Backgrounds**: Subtle gradients for depth
- **Rounded Corners**: Modern, friendly appearance
- **Hover Effects**: Interactive feedback on all buttons

### Enhanced User Experience
- **Emoji Icons**: Intuitive visual indicators
- **Professional Typography**: Segoe UI font family
- **Responsive Layout**: Adapts to different screen sizes
- **Status Indicators**: Color-coded feedback system

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Email Checker Pro** - Professional email validation made beautiful! 🎯✨
