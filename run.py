#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run script for Email Checker Pro
"""

import sys
import os
import subprocess
import traceback

def main():
    """Main function to run the application"""
    # Check if required packages are installed
    try:
        import PyQt5
        import email_validator
        import pandas
        import openpyxl
        import dns.resolver
    except ImportError as e:
        print(f"Error: Missing required package - {e}")
        print("Installing required packages...")

        # Install required packages
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])

        print("Required packages installed successfully.")

    # Run the application
    try:
        print("Starting Email Checker Pro application...")
        from main import main as run_app
        run_app()
    except Exception as e:
        print(f"Error running application: {e}")
        print("Detailed error information:")
        traceback.print_exc()

        # Keep the console open
        print("\nApplication crashed. Press Enter to exit...")
        input()
        sys.exit(1)

if __name__ == "__main__":
    main()
