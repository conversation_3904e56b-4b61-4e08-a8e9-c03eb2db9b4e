#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Import/Export functionality for Email Checker Pro
Handles importing emails from various file formats and exporting results.
"""

import os
import csv
import pandas as pd
from PyQt5.QtCore import QObject, pyqtSignal, QThread

class ImportWorker(QObject):
    """Worker class for importing emails in a separate thread"""

    # Signals
    email_found = pyqtSignal(str, str)  # email, name
    progress_updated = pyqtSignal(int)  # progress percentage
    finished = pyqtSignal(int)  # total imported count
    error = pyqtSignal(str)  # error message

    def __init__(self, file_path):
        """Initialize the worker

        Args:
            file_path (str): Path to the file to import
        """
        super().__init__()
        self.file_path = file_path
        self.stop_requested = False

    def stop(self):
        """Request the worker to stop"""
        self.stop_requested = True

    def run(self):
        """Run the import process"""
        try:
            file_ext = os.path.splitext(self.file_path)[1].lower()

            if file_ext == '.csv':
                self._import_csv()
            elif file_ext == '.txt':
                self._import_txt()
            elif file_ext in ('.xls', '.xlsx'):
                self._import_excel()
            else:
                self.error.emit(f"Unsupported file format: {file_ext}")
                self.finished.emit(0)
        except Exception as e:
            self.error.emit(f"Error importing file: {str(e)}")
            self.finished.emit(0)

    def _import_csv(self):
        """Import emails from a CSV file"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            file_content = None

            for encoding in encodings:
                try:
                    with open(self.file_path, 'r', encoding=encoding) as f:
                        file_content = f.read()
                        break
                except UnicodeDecodeError:
                    continue

            if file_content is None:
                self.error.emit("Could not decode the file with any supported encoding")
                self.finished.emit(0)
                return

            # Try to detect the delimiter
            try:
                sniffer = csv.Sniffer()
                dialect = sniffer.sniff(file_content[:4096])
                delimiter = dialect.delimiter
            except csv.Error:
                # If sniffing fails, try common delimiters
                for delimiter in [',', ';', '\t', '|']:
                    if delimiter in file_content:
                        break
                else:
                    self.error.emit("Error importing CSV file: bad delimiter or quotechar value")
                    self.finished.emit(0)
                    return

            # Parse the CSV content
            rows = []
            for line in file_content.splitlines():
                if not line.strip():
                    continue

                # Handle quoted fields properly
                fields = []
                field = ""
                in_quotes = False

                for char in line:
                    if char == '"':
                        in_quotes = not in_quotes
                    elif char == delimiter and not in_quotes:
                        fields.append(field.strip())
                        field = ""
                    else:
                        field += char

                # Add the last field
                fields.append(field.strip())
                rows.append(fields)

            # Check if the file has a header
            has_header = False
            if len(rows) > 0:
                first_row = rows[0]
                # Check if the first row contains email-like strings
                if not any('@' in cell for cell in first_row):
                    has_header = True

            # Process rows
            start_idx = 1 if has_header else 0
            total_rows = len(rows) - start_idx
            imported_count = 0

            if total_rows <= 0:
                self.error.emit("No valid data found in the file")
                self.finished.emit(0)
                return

            for i, row in enumerate(rows[start_idx:]):
                if self.stop_requested:
                    break

                # Update progress
                progress = int((i + 1) / total_rows * 100)
                self.progress_updated.emit(progress)

                # Skip empty rows
                if not row or all(not cell.strip() for cell in row):
                    continue

                # Extract email and name
                email = None
                name = None

                for cell in row:
                    cell = cell.strip().strip('"\'')  # Remove quotes
                    if '@' in cell and not email:
                        email = cell
                    elif cell and not name:
                        name = cell

                if email:
                    self.email_found.emit(email, name)
                    imported_count += 1

            self.finished.emit(imported_count)

        except Exception as e:
            self.error.emit(f"Error importing CSV file: {str(e)}")
            self.finished.emit(0)

    def _import_txt(self):
        """Import emails from a TXT file"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']
            lines = None

            for encoding in encodings:
                try:
                    with open(self.file_path, 'r', encoding=encoding) as f:
                        lines = f.readlines()
                        break
                except UnicodeDecodeError:
                    continue

            if lines is None:
                self.error.emit("Could not decode the file with any supported encoding")
                self.finished.emit(0)
                return

            total_lines = len(lines)
            imported_count = 0

            if total_lines <= 0:
                self.error.emit("No data found in the file")
                self.finished.emit(0)
                return

            # Detect if the file is tab-separated or uses another delimiter
            delimiters = ['\t', ',', ';', '|']
            delimiter_counts = {d: 0 for d in delimiters}

            # Count occurrences of each delimiter in the first few lines
            sample_lines = lines[:min(10, total_lines)]
            for line in sample_lines:
                for d in delimiters:
                    delimiter_counts[d] += line.count(d)

            # Find the most common delimiter
            most_common_delimiter = max(delimiters, key=lambda d: delimiter_counts[d])

            # If no delimiter is found, assume one email per line
            use_delimiter = delimiter_counts[most_common_delimiter] > 0

            for i, line in enumerate(lines):
                if self.stop_requested:
                    break

                # Update progress
                progress = int((i + 1) / total_lines * 100)
                self.progress_updated.emit(progress)

                # Skip empty lines
                line = line.strip()
                if not line:
                    continue

                if use_delimiter:
                    # Split by the detected delimiter
                    parts = line.split(most_common_delimiter)

                    # Clean parts
                    parts = [p.strip().strip('"\'') for p in parts if p.strip()]

                    if not parts:
                        continue

                    # Find email and name
                    email = None
                    name = None

                    for part in parts:
                        if '@' in part and not email:
                            email = part
                        elif part and not name:
                            name = part

                    if email:
                        self.email_found.emit(email, name)
                        imported_count += 1
                else:
                    # Assume the line contains only an email
                    if '@' in line:
                        # Clean the email
                        email = line.strip().strip('"\'')
                        self.email_found.emit(email, None)
                        imported_count += 1

            self.finished.emit(imported_count)

        except Exception as e:
            self.error.emit(f"Error importing TXT file: {str(e)}")
            self.finished.emit(0)

    def _import_excel(self):
        """Import emails from an Excel file"""
        try:
            # Read all sheets
            try:
                xl = pd.ExcelFile(self.file_path)
                sheet_names = xl.sheet_names
            except Exception as e:
                self.error.emit(f"Error opening Excel file: {str(e)}")
                self.finished.emit(0)
                return

            if not sheet_names:
                self.error.emit("No sheets found in the Excel file")
                self.finished.emit(0)
                return

            imported_count = 0
            total_sheets = len(sheet_names)

            for sheet_idx, sheet_name in enumerate(sheet_names):
                if self.stop_requested:
                    break

                try:
                    # Read the sheet
                    df = pd.read_excel(self.file_path, sheet_name=sheet_name)

                    # Skip empty sheets
                    if df.empty:
                        continue

                    # Find columns that might contain emails
                    email_cols = []
                    name_cols = []

                    for col in df.columns:
                        col_str = str(col).lower()
                        if 'email' in col_str or 'e-mail' in col_str or 'mail' in col_str or 'e_mail' in col_str:
                            email_cols.append(col)
                        elif 'name' in col_str or 'contact' in col_str or 'person' in col_str or 'full' in col_str:
                            name_cols.append(col)

                    # If no email column found, try to find any column with @ character
                    if not email_cols:
                        for col in df.columns:
                            # Check if the column contains at least one cell with @
                            try:
                                if df[col].astype(str).str.contains('@').any():
                                    email_cols.append(col)
                                    break
                            except:
                                # Skip columns that can't be converted to string
                                continue

                    # Process rows
                    total_rows = len(df)

                    for i, row in df.iterrows():
                        if self.stop_requested:
                            break

                        # Update progress
                        sheet_progress = (sheet_idx / total_sheets)
                        row_progress = ((i + 1) / total_rows) / total_sheets
                        progress = int((sheet_progress + row_progress) * 100)
                        self.progress_updated.emit(progress)

                        # Extract email
                        email = None

                        # First try email columns
                        for col in email_cols:
                            val = row[col]
                            if pd.notna(val) and '@' in str(val):
                                email = str(val).strip().strip('"\'')
                                break

                        # If no email found in email columns, try to find in any column
                        if not email:
                            for col in df.columns:
                                try:
                                    val = row[col]
                                    if pd.notna(val) and '@' in str(val):
                                        email = str(val).strip().strip('"\'')
                                        break
                                except:
                                    continue

                        if not email:
                            continue

                        # Extract name
                        name = None

                        # First try name columns
                        for col in name_cols:
                            val = row[col]
                            if pd.notna(val) and str(val).strip():
                                name = str(val).strip().strip('"\'')
                                break

                        # If no name found, try to use any non-email column
                        if not name:
                            for col in df.columns:
                                if col in email_cols:
                                    continue

                                try:
                                    val = row[col]
                                    if pd.notna(val) and str(val).strip():
                                        name = str(val).strip().strip('"\'')
                                        break
                                except:
                                    continue

                        self.email_found.emit(email, name)
                        imported_count += 1

                except Exception as e:
                    # Log the error but continue with other sheets
                    print(f"Error processing sheet '{sheet_name}': {str(e)}")

            if imported_count > 0:
                self.finished.emit(imported_count)
            else:
                self.error.emit("No valid email addresses found in the Excel file")
                self.finished.emit(0)

        except Exception as e:
            self.error.emit(f"Error importing Excel file: {str(e)}")
            self.finished.emit(0)

class ExportWorker(QObject):
    """Worker class for exporting emails in a separate thread"""

    # Signals
    progress_updated = pyqtSignal(int)  # progress percentage
    finished = pyqtSignal(bool, str)  # success, message

    def __init__(self, file_path, emails, columns):
        """Initialize the worker

        Args:
            file_path (str): Path to the file to export
            emails (list): List of email data dictionaries
            columns (list): List of columns to export
        """
        super().__init__()
        self.file_path = file_path
        self.emails = emails
        self.columns = columns
        self.stop_requested = False

    def stop(self):
        """Request the worker to stop"""
        self.stop_requested = True

    def run(self):
        """Run the export process"""
        try:
            file_ext = os.path.splitext(self.file_path)[1].lower()

            if file_ext == '.csv':
                self._export_csv()
            elif file_ext == '.txt':
                self._export_txt()
            elif file_ext in ('.xls', '.xlsx'):
                self._export_excel()
            else:
                self.finished.emit(False, f"Unsupported file format: {file_ext}")
        except Exception as e:
            self.finished.emit(False, f"Error exporting file: {str(e)}")

    def _export_csv(self):
        """Export emails to a CSV file"""
        try:
            with open(self.file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # Write header
                writer.writerow(self.columns)

                # Write data
                total_emails = len(self.emails)
                for i, email_data in enumerate(self.emails):
                    if self.stop_requested:
                        break

                    # Update progress
                    progress = int((i + 1) / total_emails * 100)
                    self.progress_updated.emit(progress)

                    # Extract values for each column
                    row = [email_data.get(col, '') for col in self.columns]
                    writer.writerow(row)

                self.finished.emit(True, f"Successfully exported {total_emails} emails to {self.file_path}")

        except Exception as e:
            self.finished.emit(False, f"Error exporting to CSV: {str(e)}")

    def _export_txt(self):
        """Export emails to a TXT file"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                # Write data
                total_emails = len(self.emails)
                for i, email_data in enumerate(self.emails):
                    if self.stop_requested:
                        break

                    # Update progress
                    progress = int((i + 1) / total_emails * 100)
                    self.progress_updated.emit(progress)

                    # Extract values for each column
                    values = [str(email_data.get(col, '')) for col in self.columns]
                    line = '\t'.join(values)
                    f.write(line + '\n')

                self.finished.emit(True, f"Successfully exported {total_emails} emails to {self.file_path}")

        except Exception as e:
            self.finished.emit(False, f"Error exporting to TXT: {str(e)}")

    def _export_excel(self):
        """Export emails to an Excel file"""
        try:
            # Create a DataFrame
            data = []
            total_emails = len(self.emails)

            for i, email_data in enumerate(self.emails):
                if self.stop_requested:
                    break

                # Update progress (50% for data preparation)
                progress = int((i + 1) / total_emails * 50)
                self.progress_updated.emit(progress)

                # Extract values for each column
                row = {col: email_data.get(col, '') for col in self.columns}
                data.append(row)

            # Create DataFrame
            df = pd.DataFrame(data)

            # Export to Excel
            self.progress_updated.emit(75)  # 75% progress
            df.to_excel(self.file_path, index=False)

            self.progress_updated.emit(100)  # 100% progress
            self.finished.emit(True, f"Successfully exported {total_emails} emails to {self.file_path}")

        except Exception as e:
            self.finished.emit(False, f"Error exporting to Excel: {str(e)}")

class ImportExportManager:
    """Manages import and export operations"""

    def __init__(self, db_manager=None):
        """Initialize the manager

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.import_thread = None
        self.import_worker = None
        self.export_thread = None
        self.export_worker = None

    def start_import(self, file_path, on_email_found=None, on_progress=None, on_finished=None, on_error=None):
        """Start importing emails from a file

        Args:
            file_path (str): Path to the file to import
            on_email_found (callable, optional): Callback for when an email is found
            on_progress (callable, optional): Callback for progress updates
            on_finished (callable, optional): Callback for when import is finished
            on_error (callable, optional): Callback for when an error occurs

        Returns:
            bool: True if import started successfully, False otherwise
        """
        # Stop any existing import
        self.stop_import()

        # Create a new thread and worker
        self.import_thread = QThread()
        self.import_worker = ImportWorker(file_path)
        self.import_worker.moveToThread(self.import_thread)

        # Connect signals
        self.import_thread.started.connect(self.import_worker.run)
        self.import_worker.finished.connect(self.import_thread.quit)
        self.import_worker.finished.connect(self.import_worker.deleteLater)
        self.import_thread.finished.connect(self.import_thread.deleteLater)

        if on_email_found:
            self.import_worker.email_found.connect(on_email_found)

        if on_progress:
            self.import_worker.progress_updated.connect(on_progress)

        if on_finished:
            self.import_worker.finished.connect(on_finished)

        if on_error:
            self.import_worker.error.connect(on_error)

        # Start the thread
        self.import_thread.start()

        return True

    def stop_import(self):
        """Stop the import process"""
        try:
            if hasattr(self, 'import_worker') and self.import_worker:
                self.import_worker.stop()

            if hasattr(self, 'import_thread') and self.import_thread and self.import_thread.isRunning():
                self.import_thread.quit()
                self.import_thread.wait()
        except Exception as e:
            print(f"Error stopping import: {str(e)}")

    def start_export(self, file_path, emails, columns, on_progress=None, on_finished=None):
        """Start exporting emails to a file

        Args:
            file_path (str): Path to the file to export
            emails (list): List of email data dictionaries
            columns (list): List of columns to export
            on_progress (callable, optional): Callback for progress updates
            on_finished (callable, optional): Callback for when export is finished

        Returns:
            bool: True if export started successfully, False otherwise
        """
        # Stop any existing export
        self.stop_export()

        # Create a new thread and worker
        self.export_thread = QThread()
        self.export_worker = ExportWorker(file_path, emails, columns)
        self.export_worker.moveToThread(self.export_thread)

        # Connect signals
        self.export_thread.started.connect(self.export_worker.run)
        self.export_worker.finished.connect(self.export_thread.quit)
        self.export_worker.finished.connect(self.export_worker.deleteLater)
        self.export_thread.finished.connect(self.export_thread.deleteLater)

        if on_progress:
            self.export_worker.progress_updated.connect(on_progress)

        if on_finished:
            self.export_worker.finished.connect(on_finished)

        # Start the thread
        self.export_thread.start()

        return True

    def stop_export(self):
        """Stop the export process"""
        try:
            if hasattr(self, 'export_worker') and self.export_worker:
                self.export_worker.stop()

            if hasattr(self, 'export_thread') and self.export_thread and self.export_thread.isRunning():
                self.export_thread.quit()
                self.export_thread.wait()
        except Exception as e:
            print(f"Error stopping export: {str(e)}")
