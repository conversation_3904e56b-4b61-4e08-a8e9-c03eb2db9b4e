#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Manager for Email Checker Pro
Handles all database operations for storing and retrieving email verification results.
"""

import os
import sqlite3
from datetime import datetime

class DatabaseManager:
    """Manages database operations for the email verification application"""
    
    def __init__(self, db_path=None):
        """Initialize the database manager
        
        Args:
            db_path (str, optional): Path to the SQLite database file.
                If None, a default path will be used.
        """
        if db_path is None:
            # Use a default path in the user's home directory
            home_dir = os.path.expanduser("~")
            db_dir = os.path.join(home_dir, ".email_checker_pro")
            
            # Create directory if it doesn't exist
            if not os.path.exists(db_dir):
                os.makedirs(db_dir)
                
            db_path = os.path.join(db_dir, "email_checker.db")
            
        self.db_path = db_path
        self._create_tables_if_not_exist()
    
    def _create_tables_if_not_exist(self):
        """Create database tables if they don't exist"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create emails table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS emails (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            name TEXT,
            result TEXT,
            description TEXT,
            suggestion TEXT,
            last_check TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create settings table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            value TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Create unsupported_servers table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS unsupported_servers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            server_domain TEXT UNIQUE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_email(self, email, name=None):
        """Add a new email to the database
        
        Args:
            email (str): Email address
            name (str, optional): Name associated with the email
            
        Returns:
            int: ID of the inserted email or existing email
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "INSERT OR IGNORE INTO emails (email, name, result, description) VALUES (?, ?, 'Wait', 'Wait for Checking')",
                (email, name)
            )
            conn.commit()
            
            # Get the ID of the inserted email or the existing one
            cursor.execute("SELECT id FROM emails WHERE email = ?", (email,))
            email_id = cursor.fetchone()[0]
            
            return email_id
        finally:
            conn.close()
    
    def update_email_result(self, email, result, description, suggestion=None):
        """Update the verification result for an email
        
        Args:
            email (str): Email address
            result (str): Verification result (OK, BAD, etc.)
            description (str): Detailed description of the result
            suggestion (str, optional): Suggestion for the user
            
        Returns:
            bool: True if successful, False otherwise
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "UPDATE emails SET result = ?, description = ?, suggestion = ?, last_check = ? WHERE email = ?",
                (result, description, suggestion, datetime.now().isoformat(), email)
            )
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()
    
    def get_all_emails(self):
        """Get all emails from the database
        
        Returns:
            list: List of dictionaries containing email data
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM emails ORDER BY id")
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()
    
    def get_email_by_address(self, email):
        """Get email data by address
        
        Args:
            email (str): Email address
            
        Returns:
            dict: Email data or None if not found
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM emails WHERE email = ?", (email,))
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            conn.close()
    
    def delete_email(self, email):
        """Delete an email from the database
        
        Args:
            email (str): Email address
            
        Returns:
            bool: True if successful, False otherwise
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("DELETE FROM emails WHERE email = ?", (email,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()
    
    def clear_all_emails(self):
        """Delete all emails from the database
        
        Returns:
            int: Number of deleted emails
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("DELETE FROM emails")
            deleted_count = cursor.rowcount
            conn.commit()
            return deleted_count
        finally:
            conn.close()
            
    def add_unsupported_server(self, server_domain):
        """Add an unsupported server to the database
        
        Args:
            server_domain (str): Server domain name
            
        Returns:
            bool: True if successful, False otherwise
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "INSERT OR IGNORE INTO unsupported_servers (server_domain) VALUES (?)",
                (server_domain,)
            )
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()
            
    def get_unsupported_servers(self):
        """Get all unsupported servers from the database
        
        Returns:
            list: List of server domain names
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT server_domain FROM unsupported_servers ORDER BY server_domain")
            return [row[0] for row in cursor.fetchall()]
        finally:
            conn.close()
