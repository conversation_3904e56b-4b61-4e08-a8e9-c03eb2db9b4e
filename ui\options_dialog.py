#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Options Dialog for Email Checker Pro
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QGroupBox,
    QRadioButton, QSpinBox, QLabel, QCheckBox, QListWidget, QListWidgetItem,
    QPushButton, QLineEdit, QDialogButtonBox, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSlot

class OptionsDialog(QDialog):
    """Options dialog for configuring the application"""
    
    def __init__(self, config, db_manager, parent=None):
        """Initialize the dialog
        
        Args:
            config: Configuration manager
            db_manager: Database manager
            parent: Parent widget
        """
        super().__init__(parent)
        
        self.config = config
        self.db_manager = db_manager
        
        # Set up the UI
        self.setup_ui()
        
        # Load settings
        self.load_settings()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Set window properties
        self.setWindowTitle("Options")
        self.resize(600, 400)
        
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Create tab widget
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # Create tabs
        check_tab = QWidget()
        import_tab = QWidget()
        export_tab = QWidget()
        unsupported_tab = QWidget()
        
        tab_widget.addTab(check_tab, "Check")
        tab_widget.addTab(import_tab, "Import")
        tab_widget.addTab(export_tab, "Export")
        tab_widget.addTab(unsupported_tab, "Unsupported Email Servers")
        
        # Set up check tab
        self.setup_check_tab(check_tab)
        
        # Set up import tab
        self.setup_import_tab(import_tab)
        
        # Set up export tab
        self.setup_export_tab(export_tab)
        
        # Set up unsupported servers tab
        self.setup_unsupported_tab(unsupported_tab)
        
        # Create button box
        button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_settings)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
    
    def setup_check_tab(self, tab):
        """Set up the check tab
        
        Args:
            tab: Tab widget
        """
        layout = QVBoxLayout(tab)
        
        # Create check history group
        history_group = QGroupBox("Check History")
        history_layout = QVBoxLayout(history_group)
        
        self.use_old_radio = QRadioButton("Use the old results directly.")
        self.always_recheck_radio = QRadioButton("In any case, always recheck.")
        self.recheck_if_not_ok_radio = QRadioButton("Recheck only if the old [Result] is NOT \"OK\".")
        self.recheck_if_recheck_radio = QRadioButton("Recheck only if the old [Suggestion] is \"Recheck\".")
        
        self.recheck_if_old_radio = QRadioButton("Recheck only if the last check date is")
        recheck_days_layout = QHBoxLayout()
        recheck_days_layout.addWidget(self.recheck_if_old_radio)
        self.recheck_days_spin = QSpinBox()
        self.recheck_days_spin.setMinimum(1)
        self.recheck_days_spin.setMaximum(365)
        self.recheck_days_spin.setValue(30)
        recheck_days_layout.addWidget(self.recheck_days_spin)
        recheck_days_layout.addWidget(QLabel("days ago."))
        recheck_days_layout.addStretch()
        
        history_layout.addWidget(self.use_old_radio)
        history_layout.addWidget(self.always_recheck_radio)
        history_layout.addWidget(self.recheck_if_not_ok_radio)
        history_layout.addWidget(self.recheck_if_recheck_radio)
        history_layout.addLayout(recheck_days_layout)
        
        layout.addWidget(history_group)
        
        # Create threads group
        threads_group = QGroupBox("Maximum Number of Checking Threads")
        threads_layout = QHBoxLayout(threads_group)
        
        self.threads_spin = QSpinBox()
        self.threads_spin.setMinimum(1)
        self.threads_spin.setMaximum(50)
        self.threads_spin.setValue(10)
        threads_layout.addWidget(self.threads_spin)
        threads_layout.addStretch()
        
        layout.addWidget(threads_group)
        
        # Add stretch to push everything to the top
        layout.addStretch()
    
    def setup_import_tab(self, tab):
        """Set up the import tab
        
        Args:
            tab: Tab widget
        """
        layout = QVBoxLayout(tab)
        
        # Create import options group
        import_group = QGroupBox("Import Options")
        import_layout = QVBoxLayout(import_group)
        
        self.show_sample_files_check = QCheckBox("Always display the screenshots of sample files before import data.")
        import_layout.addWidget(self.show_sample_files_check)
        
        layout.addWidget(import_group)
        
        # Add stretch to push everything to the top
        layout.addStretch()
    
    def setup_export_tab(self, tab):
        """Set up the export tab
        
        Args:
            tab: Tab widget
        """
        layout = QVBoxLayout(tab)
        
        # Create export options group
        export_group = QGroupBox("Export Options")
        export_layout = QVBoxLayout(export_group)
        
        self.show_export_wizard_check = QCheckBox("Always display Wizard before export data.")
        export_layout.addWidget(self.show_export_wizard_check)
        
        self.remove_exported_check = QCheckBox("Remove the exported items from list.")
        export_layout.addWidget(self.remove_exported_check)
        
        layout.addWidget(export_group)
        
        # Create results group
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        
        self.export_results_list = QListWidget()
        self.export_results_list.addItem("Wait")
        self.export_results_list.addItem("OK")
        self.export_results_list.addItem("BAD")
        self.export_results_list.addItem("Server Not Found")
        self.export_results_list.addItem("Checking Failed")
        
        # Make items checkable
        for i in range(self.export_results_list.count()):
            item = self.export_results_list.item(i)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(Qt.Checked)
        
        results_layout.addWidget(self.export_results_list)
        
        layout.addWidget(results_group)
        
        # Create columns group
        columns_group = QGroupBox("Columns")
        columns_layout = QVBoxLayout(columns_group)
        
        self.export_columns_list = QListWidget()
        self.export_columns_list.addItem("Name")
        self.export_columns_list.addItem("Email Address")
        self.export_columns_list.addItem("Result")
        self.export_columns_list.addItem("Description")
        self.export_columns_list.addItem("Suggestion")
        self.export_columns_list.addItem("Last Check")
        
        # Make items checkable
        for i in range(self.export_columns_list.count()):
            item = self.export_columns_list.item(i)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(Qt.Checked)
        
        columns_layout.addWidget(self.export_columns_list)
        
        layout.addWidget(columns_group)
    
    def setup_unsupported_tab(self, tab):
        """Set up the unsupported servers tab
        
        Args:
            tab: Tab widget
        """
        layout = QVBoxLayout(tab)
        
        # Create info label
        info_label = QLabel("All email addresses on these servers will be marked as OK.")
        layout.addWidget(info_label)
        
        # Create servers list
        self.servers_list = QListWidget()
        layout.addWidget(self.servers_list)
        
        # Create buttons layout
        buttons_layout = QHBoxLayout()
        
        # Create server input
        self.server_input = QLineEdit()
        self.server_input.setPlaceholderText("Enter server domain (e.g., example.com)")
        buttons_layout.addWidget(self.server_input)
        
        # Create add button
        add_button = QPushButton("Add")
        add_button.clicked.connect(self.add_server)
        buttons_layout.addWidget(add_button)
        
        # Create delete button
        delete_button = QPushButton("Delete")
        delete_button.clicked.connect(self.delete_server)
        buttons_layout.addWidget(delete_button)
        
        layout.addLayout(buttons_layout)
    
    def load_settings(self):
        """Load settings from configuration"""
        # Check history
        check_history = self.config.get('check_history', 'recheck_if_not_ok')
        if check_history == 'use_old':
            self.use_old_radio.setChecked(True)
        elif check_history == 'always_recheck':
            self.always_recheck_radio.setChecked(True)
        elif check_history == 'recheck_if_not_ok':
            self.recheck_if_not_ok_radio.setChecked(True)
        elif check_history == 'recheck_if_recheck':
            self.recheck_if_recheck_radio.setChecked(True)
        elif check_history == 'recheck_if_old':
            self.recheck_if_old_radio.setChecked(True)
        
        # Recheck days
        self.recheck_days_spin.setValue(self.config.get('recheck_days', 30))
        
        # Threads
        self.threads_spin.setValue(self.config.get('max_threads', 10))
        
        # Import options
        self.show_sample_files_check.setChecked(self.config.get('show_sample_files', True))
        
        # Export options
        self.show_export_wizard_check.setChecked(self.config.get('show_export_wizard', True))
        self.remove_exported_check.setChecked(self.config.get('remove_exported', False))
        
        # Export columns
        export_columns = self.config.get('export_columns', ['Name', 'Email Address', 'Result', 'Description', 'Suggestion', 'Last Check'])
        for i in range(self.export_columns_list.count()):
            item = self.export_columns_list.item(i)
            item.setCheckState(Qt.Checked if item.text() in export_columns else Qt.Unchecked)
        
        # Unsupported servers
        unsupported_servers = self.config.get('unsupported_servers', [])
        self.servers_list.clear()
        for server in unsupported_servers:
            self.servers_list.addItem(server)
    
    def save_settings(self):
        """Save settings to configuration"""
        # Check history
        if self.use_old_radio.isChecked():
            self.config.set('check_history', 'use_old')
        elif self.always_recheck_radio.isChecked():
            self.config.set('check_history', 'always_recheck')
        elif self.recheck_if_not_ok_radio.isChecked():
            self.config.set('check_history', 'recheck_if_not_ok')
        elif self.recheck_if_recheck_radio.isChecked():
            self.config.set('check_history', 'recheck_if_recheck')
        elif self.recheck_if_old_radio.isChecked():
            self.config.set('check_history', 'recheck_if_old')
        
        # Recheck days
        self.config.set('recheck_days', self.recheck_days_spin.value())
        
        # Threads
        self.config.set('max_threads', self.threads_spin.value())
        
        # Import options
        self.config.set('show_sample_files', self.show_sample_files_check.isChecked())
        
        # Export options
        self.config.set('show_export_wizard', self.show_export_wizard_check.isChecked())
        self.config.set('remove_exported', self.remove_exported_check.isChecked())
        
        # Export columns
        export_columns = []
        for i in range(self.export_columns_list.count()):
            item = self.export_columns_list.item(i)
            if item.checkState() == Qt.Checked:
                export_columns.append(item.text())
        
        self.config.set('export_columns', export_columns)
        
        # Unsupported servers
        unsupported_servers = []
        for i in range(self.servers_list.count()):
            unsupported_servers.append(self.servers_list.item(i).text())
        
        self.config.set('unsupported_servers', unsupported_servers)
        
        # Update database with unsupported servers
        if self.db_manager:
            # Clear existing servers
            # TODO: Implement clear_unsupported_servers in db_manager
            
            # Add new servers
            for server in unsupported_servers:
                self.db_manager.add_unsupported_server(server)
        
        # Accept the dialog
        self.accept()
    
    @pyqtSlot()
    def add_server(self):
        """Add a server to the unsupported servers list"""
        server = self.server_input.text().strip()
        
        if not server:
            QMessageBox.warning(self, "Invalid Server", "Please enter a server domain.")
            return
        
        # Check if server already exists
        for i in range(self.servers_list.count()):
            if self.servers_list.item(i).text() == server:
                QMessageBox.information(self, "Duplicate Server", f"Server '{server}' already exists in the list.")
                return
        
        # Add server to list
        self.servers_list.addItem(server)
        
        # Clear input
        self.server_input.clear()
    
    @pyqtSlot()
    def delete_server(self):
        """Delete selected servers from the unsupported servers list"""
        selected_items = self.servers_list.selectedItems()
        
        if not selected_items:
            QMessageBox.information(self, "No Selection", "Please select a server to delete.")
            return
        
        # Delete selected items
        for item in selected_items:
            self.servers_list.takeItem(self.servers_list.row(item))
