#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Import Dialog for Email Checker Pro
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFileDialog,
    QTabWidget, QWidget, QTextEdit, QCheckBox, QDialogButtonBox
)
from PyQt5.QtGui import QPixmap, QFont
from PyQt5.QtCore import Qt, pyqtSlot

class ImportDialog(QDialog):
    """Dialog for importing email addresses from files"""

    def __init__(self, parent=None):
        """Initialize the dialog

        Args:
            parent: Parent widget
        """
        super().__init__(parent)

        self.file_path = None

        # Set up the UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface"""
        # Set window properties
        self.setWindowTitle("Import Emails")
        self.resize(600, 400)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create info label with improved styling
        info_label = QLabel("Email Checker Pro supports importing recipients from CSV, TXT, XLS(X) files. "
                           "The application will automatically detect email addresses and names in your files.")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f7ff;
                border: 1px solid #d0e0ff;
                border-radius: 5px;
                padding: 10px;
                font-weight: bold;
            }
        """)
        main_layout.addWidget(info_label)

        # Create tab widget with improved styling
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 5px 10px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background-color: #e0e0e0;
            }
        """)
        main_layout.addWidget(tab_widget)

        # Create tabs
        csv_tab = QWidget()
        txt_tab = QWidget()
        excel_tab = QWidget()

        tab_widget.addTab(csv_tab, "CSV File: Comma Separated Values")
        tab_widget.addTab(txt_tab, "TXT File: Tab Separated")
        tab_widget.addTab(excel_tab, "Excel Workbooks (XLS or XLSX)")

        # Set up CSV tab
        self.setup_csv_tab(csv_tab)

        # Set up TXT tab
        self.setup_txt_tab(txt_tab)

        # Set up Excel tab
        self.setup_excel_tab(excel_tab)

        # Create "Do not prompt" checkbox with improved styling
        self.do_not_prompt_check = QCheckBox("Do not prompt in the future.")
        self.do_not_prompt_check.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 15px;
                height: 15px;
            }
        """)
        main_layout.addWidget(self.do_not_prompt_check)

        # Create buttons layout
        buttons_layout = QHBoxLayout()

        # Create examples button with improved styling
        examples_button = QPushButton("Examples")
        examples_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        examples_button.clicked.connect(self.show_examples)
        buttons_layout.addWidget(examples_button)

        # Create button box with improved styling
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        buttons_layout.addWidget(button_box)

        main_layout.addLayout(buttons_layout)

    def setup_csv_tab(self, tab):
        """Set up the CSV tab

        Args:
            tab: Tab widget
        """
        layout = QVBoxLayout(tab)

        # Create sample text with improved styling
        sample_text = QTextEdit()
        sample_text.setReadOnly(True)
        sample_text.setFont(QFont("Courier New", 10))
        sample_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f8f8;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
            }
        """)
        sample_text.setPlainText(
            "<EMAIL>\n"
            "Test2,<EMAIL>\n"
            "\"Test3\",<EMAIL>\n"
            "\"Test4\", \"<EMAIL>\"\n"
            ",<EMAIL>"
        )
        layout.addWidget(sample_text)

        # Create browse button with improved styling
        browse_button = QPushButton("Browse CSV File...")
        browse_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        browse_button.clicked.connect(self.browse_csv_file)
        layout.addWidget(browse_button)

    def setup_txt_tab(self, tab):
        """Set up the TXT tab

        Args:
            tab: Tab widget
        """
        layout = QVBoxLayout(tab)

        # Create sample text with improved styling
        sample_text = QTextEdit()
        sample_text.setReadOnly(True)
        sample_text.setFont(QFont("Courier New", 10))
        sample_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f8f8;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
            }
        """)
        sample_text.setPlainText(
            "<EMAIL>\n"
            "Test2\<EMAIL>\n"
            "\"Test3\"\<EMAIL>\n"
            "\"Test4\"\t\"<EMAIL>\"\n"
            "\<EMAIL>"
        )
        layout.addWidget(sample_text)

        # Create browse button with improved styling
        browse_button = QPushButton("Browse TXT File...")
        browse_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
            QPushButton:pressed {
                background-color: #0a69b7;
            }
        """)
        browse_button.clicked.connect(self.browse_txt_file)
        layout.addWidget(browse_button)

    def setup_excel_tab(self, tab):
        """Set up the Excel tab

        Args:
            tab: Tab widget
        """
        layout = QVBoxLayout(tab)

        # Create info label with improved styling
        info_label = QLabel("Excel files can contain email addresses only, or name & email address, with or without header, in multiple sheets.")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #fff8e1;
                border: 1px solid #ffe082;
                border-radius: 3px;
                padding: 8px;
                font-style: italic;
            }
        """)
        layout.addWidget(info_label)

        # Create sample text with improved styling
        sample_text = QTextEdit()
        sample_text.setReadOnly(True)
        sample_text.setFont(QFont("Courier New", 10))
        sample_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f8f8;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
            }
        """)
        sample_text.setPlainText(
            "Sheet 1: Contacts\n"
            "Name\tEmail\n"
            "John Doe\<EMAIL>\n"
            "Jane Smith\<EMAIL>\n\n"
            "Sheet 2: Subscribers\n"
            "<EMAIL>\n"
            "<EMAIL>"
        )
        layout.addWidget(sample_text)

        # Create browse button with improved styling
        browse_button = QPushButton("Browse Excel File...")
        browse_button.setStyleSheet("""
            QPushButton {
                background-color: #673AB7;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5e35b1;
            }
            QPushButton:pressed {
                background-color: #512da8;
            }
        """)
        browse_button.clicked.connect(self.browse_excel_file)
        layout.addWidget(browse_button)

    @pyqtSlot()
    def browse_csv_file(self):
        """Browse for a CSV file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open CSV File",
            "",
            "CSV Files (*.csv);;All Files (*.*)"
        )

        if file_path:
            self.file_path = file_path
            self.accept()

    @pyqtSlot()
    def browse_txt_file(self):
        """Browse for a TXT file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open TXT File",
            "",
            "Text Files (*.txt);;All Files (*.*)"
        )

        if file_path:
            self.file_path = file_path
            self.accept()

    @pyqtSlot()
    def browse_excel_file(self):
        """Browse for an Excel file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Excel File",
            "",
            "Excel Files (*.xls *.xlsx);;All Files (*.*)"
        )

        if file_path:
            self.file_path = file_path
            self.accept()

    @pyqtSlot()
    def show_examples(self):
        """Show examples of file formats"""
        # TODO: Implement examples dialog
        pass

    def get_file_path(self):
        """Get the selected file path

        Returns:
            str: File path or None if no file was selected
        """
        return self.file_path
