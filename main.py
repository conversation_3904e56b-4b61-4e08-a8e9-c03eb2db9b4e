#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Email Checker Pro - Main Application
A professional email verification tool that validates email addresses
and checks their deliverability.
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtGui import QIcon
from ui.main_window import MainWindow

def main():
    """Main application entry point"""
    try:
        print("Initializing application...")
        # Create the application
        app = QApplication(sys.argv)
        app.setApplicationName("Email Checker Pro")
        app.setOrganizationName("Email Tools")

        print("Setting application icon...")
        # Set application icon
        icon_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'resources', 'icons', 'app_icon.png'))
        if os.path.exists(icon_path):
            print(f"Icon found at: {icon_path}")
            app.setWindowIcon(QIcon(icon_path))
        else:
            print(f"Icon not found at: {icon_path}")

        print("Creating main window...")
        # Create and show the main window
        window = MainWindow()
        print("Showing main window...")
        window.show()

        print("Starting event loop...")
        # Start the event loop
        return app.exec_()
    except Exception as e:
        print(f"Error in main application: {e}")
        print("Detailed error information:")
        traceback.print_exc()

        try:
            # Try to show error message box
            app = QApplication.instance()
            if not app:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "Error", f"An error occurred: {str(e)}\n\nSee console for details.")
        except:
            pass

        return 1

if __name__ == "__main__":
    sys.exit(main())
