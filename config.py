#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration settings for Email Checker Pro
"""

import os
import json

class Config:
    """Configuration manager for the application"""
    
    # Default configuration
    DEFAULT_CONFIG = {
        # General settings
        'max_threads': 10,
        'check_history': 'recheck_if_not_ok',  # 'use_old', 'always_recheck', 'recheck_if_not_ok', 'recheck_if_recheck', 'recheck_if_old'
        'recheck_days': 30,
        
        # Import settings
        'show_sample_files': True,
        
        # Export settings
        'show_export_wizard': True,
        'remove_exported': False,
        'export_columns': ['Name', 'Email Address', 'Result', 'Description', 'Suggestion', 'Last Check'],
        
        # Validation settings
        'validate_syntax': True,
        'validate_mx': True,
        'validate_smtp': True,
        'smtp_timeout': 10,
        
        # UI settings
        'single_mode': False,
        'show_tips': True,
        'window_width': 800,
        'window_height': 600,
        
        # Unsupported servers
        'unsupported_servers': [
            'compuserve.com',
            'dialnet.co.uk',
            'rocketmail.com',
            'yahoo.*'
        ]
    }
    
    def __init__(self, config_path=None):
        """Initialize the configuration manager
        
        Args:
            config_path (str, optional): Path to the configuration file.
                If None, a default path will be used.
        """
        if config_path is None:
            # Use a default path in the user's home directory
            home_dir = os.path.expanduser("~")
            config_dir = os.path.join(home_dir, ".email_checker_pro")
            
            # Create directory if it doesn't exist
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
                
            config_path = os.path.join(config_dir, "config.json")
            
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self):
        """Load configuration from file
        
        Returns:
            dict: Configuration dictionary
        """
        # Start with default configuration
        config = self.DEFAULT_CONFIG.copy()
        
        # Load from file if it exists
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    file_config = json.load(f)
                    
                    # Update default config with file config
                    config.update(file_config)
            except Exception as e:
                print(f"Error loading configuration: {e}")
        
        return config
    
    def save_config(self):
        """Save configuration to file
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=4)
            return True
        except Exception as e:
            print(f"Error saving configuration: {e}")
            return False
    
    def get(self, key, default=None):
        """Get a configuration value
        
        Args:
            key (str): Configuration key
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set a configuration value
        
        Args:
            key (str): Configuration key
            value: Configuration value
            
        Returns:
            bool: True if successful, False otherwise
        """
        self.config[key] = value
        return self.save_config()
    
    def reset(self):
        """Reset configuration to default
        
        Returns:
            bool: True if successful, False otherwise
        """
        self.config = self.DEFAULT_CONFIG.copy()
        return self.save_config()
